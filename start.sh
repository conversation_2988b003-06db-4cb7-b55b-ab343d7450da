#!/bin/bash

# نظام إدارة الأكاديمية التعليمية
# Academy Management System

echo ""
echo "========================================"
echo "   نظام إدارة الأكاديمية التعليمية"
echo "   Academy Management System"
echo "========================================"
echo ""

# فحص وجود Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Python غير مثبت أو غير موجود في PATH"
        echo "يرجى تثبيت Python 3.8 أو أحدث"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ تم العثور على Python"
echo ""

# تشغيل ملف التشغيل السريع
$PYTHON_CMD run.py

echo ""
echo "اضغط Enter للخروج..."
read
