"""
تحكم المجموعات - Group Controller
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import date, time

from models.group import Group
from models.course import Course
from models.teacher import Teacher
from models.student import Student
from models.user import User
from utils.validators import DataValidator
from utils.logger import activity_logger

class GroupController:
    """تحكم عمليات المجموعات"""
    
    def __init__(self, current_user: User):
        """
        تهيئة تحكم المجموعات
        
        Args:
            current_user: المستخدم الحالي
        """
        self.current_user = current_user
        self.logger = logging.getLogger(__name__)
    
    def create_group(self, group_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        إنشاء مجموعة جديدة
        
        Args:
            group_data: بيانات المجموعة
            
        Returns:
            Dict: نتيجة العملية
        """
        try:
            # التحقق من الصلاحيات
            if not self.current_user.has_permission('manage_groups'):
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لإضافة المجموعات'
                }
            
            # التحقق من صحة البيانات
            if not group_data.get('name') or not group_data.get('course_id'):
                return {
                    'success': False,
                    'message': 'اسم المجموعة ومعرف الكورس مطلوبان'
                }
            
            # التحقق من وجود الكورس
            course = Course.get_by_id(group_data['course_id'])
            if not course:
                return {
                    'success': False,
                    'message': 'الكورس غير موجود'
                }
            
            # التحقق من وجود المعلم إذا تم تحديده
            if group_data.get('teacher_id'):
                teacher = Teacher.get_by_id(group_data['teacher_id'])
                if not teacher:
                    return {
                        'success': False,
                        'message': 'المعلم غير موجود'
                    }
            
            # التحقق من صحة التواريخ
            if group_data.get('start_date') and group_data.get('end_date'):
                if not DataValidator.validate_date_range(
                    group_data['start_date'], 
                    group_data['end_date']
                ):
                    return {
                        'success': False,
                        'message': 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية'
                    }
            
            # إنشاء المجموعة
            group = Group.create(**group_data)
            
            # تسجيل النشاط
            activity_logger.log_user_action(
                self.current_user.id,
                self.current_user.username,
                'إضافة مجموعة',
                f"تم إضافة المجموعة: {group.name} ({group.group_code})"
            )
            
            return {
                'success': True,
                'message': 'تم إنشاء المجموعة بنجاح',
                'group': group.to_dict()
            }
            
        except ValueError as e:
            return {
                'success': False,
                'message': str(e)
            }
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء المجموعة: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def get_group(self, group_id: int) -> Dict[str, Any]:
        """
        الحصول على بيانات مجموعة
        
        Args:
            group_id: معرف المجموعة
            
        Returns:
            Dict: بيانات المجموعة
        """
        try:
            # التحقق من الصلاحيات
            if not self.current_user.has_permission('view_groups'):
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لعرض المجموعات'
                }
            
            group = Group.get_by_id(group_id)
            if not group:
                return {
                    'success': False,
                    'message': 'المجموعة غير موجودة'
                }
            
            # إضافة معلومات الكورس والمعلم
            group_dict = group.to_dict()
            group_dict['course_info'] = group.get_course_info()
            group_dict['teacher_info'] = group.get_teacher_info()
            
            return {
                'success': True,
                'group': group_dict
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على المجموعة: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def update_group(self, group_id: int, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        تحديث بيانات مجموعة
        
        Args:
            group_id: معرف المجموعة
            update_data: البيانات المحدثة
            
        Returns:
            Dict: نتيجة العملية
        """
        try:
            # التحقق من الصلاحيات
            if not self.current_user.has_permission('manage_groups'):
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لتعديل المجموعات'
                }
            
            group = Group.get_by_id(group_id)
            if not group:
                return {
                    'success': False,
                    'message': 'المجموعة غير موجودة'
                }
            
            # التحقق من وجود المعلم إذا تم تحديده
            if update_data.get('teacher_id'):
                teacher = Teacher.get_by_id(update_data['teacher_id'])
                if not teacher:
                    return {
                        'success': False,
                        'message': 'المعلم غير موجود'
                    }
            
            # التحقق من صحة التواريخ
            start_date = update_data.get('start_date', group.start_date)
            end_date = update_data.get('end_date', group.end_date)
            
            if start_date and end_date:
                if not DataValidator.validate_date_range(start_date, end_date):
                    return {
                        'success': False,
                        'message': 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية'
                    }
            
            # تحديث البيانات
            if group.update(**update_data):
                # تسجيل النشاط
                activity_logger.log_user_action(
                    self.current_user.id,
                    self.current_user.username,
                    'تعديل مجموعة',
                    f"تم تعديل المجموعة: {group.name} ({group.group_code})"
                )
                
                return {
                    'success': True,
                    'message': 'تم تحديث بيانات المجموعة بنجاح',
                    'group': group.to_dict()
                }
            else:
                return {
                    'success': False,
                    'message': 'فشل في تحديث البيانات'
                }
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديث المجموعة: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def delete_group(self, group_id: int) -> Dict[str, Any]:
        """
        حذف مجموعة
        
        Args:
            group_id: معرف المجموعة
            
        Returns:
            Dict: نتيجة العملية
        """
        try:
            # التحقق من الصلاحيات (المدير فقط)
            if self.current_user.role != 'admin':
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لحذف المجموعات'
                }
            
            group = Group.get_by_id(group_id)
            if not group:
                return {
                    'success': False,
                    'message': 'المجموعة غير موجودة'
                }
            
            group_name = group.name
            group_code = group.group_code
            
            if group.delete():
                # تسجيل النشاط
                activity_logger.log_user_action(
                    self.current_user.id,
                    self.current_user.username,
                    'حذف مجموعة',
                    f"تم حذف المجموعة: {group_name} ({group_code})"
                )
                
                return {
                    'success': True,
                    'message': 'تم حذف المجموعة بنجاح'
                }
            else:
                return {
                    'success': False,
                    'message': 'فشل في حذف المجموعة'
                }
            
        except ValueError as e:
            return {
                'success': False,
                'message': str(e)
            }
        except Exception as e:
            self.logger.error(f"خطأ في حذف المجموعة: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def search_groups(self, search_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        البحث عن المجموعات
        
        Args:
            search_params: معاملات البحث
            
        Returns:
            Dict: نتائج البحث
        """
        try:
            # التحقق من الصلاحيات
            if not self.current_user.has_permission('view_groups'):
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لعرض المجموعات'
                }
            
            # تصفية حسب المعلم إذا كان المستخدم معلماً
            if self.current_user.role == 'teacher':
                teacher = Teacher.get_by_user_id(self.current_user.id)
                if teacher:
                    search_params['teacher_id'] = teacher.id
            
            groups = Group.search(
                search_term=search_params.get('search_term'),
                course_id=search_params.get('course_id'),
                teacher_id=search_params.get('teacher_id'),
                status=search_params.get('status'),
                limit=search_params.get('limit', 50)
            )
            
            return {
                'success': True,
                'groups': [group.to_dict() for group in groups],
                'count': len(groups)
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في البحث عن المجموعات: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def add_student_to_group(self, group_id: int, student_id: int) -> Dict[str, Any]:
        """
        إضافة طالب للمجموعة
        
        Args:
            group_id: معرف المجموعة
            student_id: معرف الطالب
            
        Returns:
            Dict: نتيجة العملية
        """
        try:
            # التحقق من الصلاحيات
            if not self.current_user.has_permission('manage_students'):
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لإدارة الطلاب'
                }
            
            group = Group.get_by_id(group_id)
            if not group:
                return {
                    'success': False,
                    'message': 'المجموعة غير موجودة'
                }
            
            student = Student.get_by_id(student_id)
            if not student:
                return {
                    'success': False,
                    'message': 'الطالب غير موجود'
                }
            
            if group.add_student(student_id):
                # تسجيل النشاط
                activity_logger.log_user_action(
                    self.current_user.id,
                    self.current_user.username,
                    'إضافة طالب للمجموعة',
                    f"تم إضافة الطالب {student.full_name} للمجموعة {group.name}"
                )
                
                return {
                    'success': True,
                    'message': 'تم إضافة الطالب للمجموعة بنجاح'
                }
            else:
                return {
                    'success': False,
                    'message': 'فشل في إضافة الطالب للمجموعة'
                }
            
        except ValueError as e:
            return {
                'success': False,
                'message': str(e)
            }
        except Exception as e:
            self.logger.error(f"خطأ في إضافة الطالب للمجموعة: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def remove_student_from_group(self, group_id: int, student_id: int) -> Dict[str, Any]:
        """
        إزالة طالب من المجموعة
        
        Args:
            group_id: معرف المجموعة
            student_id: معرف الطالب
            
        Returns:
            Dict: نتيجة العملية
        """
        try:
            # التحقق من الصلاحيات
            if not self.current_user.has_permission('manage_students'):
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لإدارة الطلاب'
                }
            
            group = Group.get_by_id(group_id)
            if not group:
                return {
                    'success': False,
                    'message': 'المجموعة غير موجودة'
                }
            
            student = Student.get_by_id(student_id)
            if not student:
                return {
                    'success': False,
                    'message': 'الطالب غير موجود'
                }
            
            if group.remove_student(student_id):
                # تسجيل النشاط
                activity_logger.log_user_action(
                    self.current_user.id,
                    self.current_user.username,
                    'إزالة طالب من المجموعة',
                    f"تم إزالة الطالب {student.full_name} من المجموعة {group.name}"
                )
                
                return {
                    'success': True,
                    'message': 'تم إزالة الطالب من المجموعة بنجاح'
                }
            else:
                return {
                    'success': False,
                    'message': 'فشل في إزالة الطالب من المجموعة'
                }
            
        except Exception as e:
            self.logger.error(f"خطأ في إزالة الطالب من المجموعة: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def get_group_students(self, group_id: int, active_only: bool = True) -> Dict[str, Any]:
        """
        الحصول على طلاب المجموعة
        
        Args:
            group_id: معرف المجموعة
            active_only: الطلاب النشطين فقط
            
        Returns:
            Dict: طلاب المجموعة
        """
        try:
            group = Group.get_by_id(group_id)
            if not group:
                return {
                    'success': False,
                    'message': 'المجموعة غير موجودة'
                }
            
            if active_only:
                students = group.get_active_students()
            else:
                students = group.get_students()
            
            return {
                'success': True,
                'students': students
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على طلاب المجموعة: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
