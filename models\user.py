"""
نموذج المستخدم - User Model
"""

import bcrypt
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from database.db_handler import db_handler

class User:
    """نموذج المستخدم مع نظام الصلاحيات"""
    
    # أنواع الأدوار المتاحة
    ROLES = {
        'admin': 'مدير النظام',
        'teacher': 'معلم',
        'employee': 'موظف',
        'student': 'طالب'
    }
    
    def __init__(self, user_id: int = None, username: str = None, 
                 password_hash: str = None, email: str = None,
                 full_name: str = None, role: str = None,
                 is_active: bool = True, last_login: datetime = None,
                 created_at: datetime = None, updated_at: datetime = None):
        """
        تهيئة نموذج المستخدم
        
        Args:
            user_id: معرف المستخدم
            username: اسم المستخدم
            password_hash: كلمة المرور المشفرة
            email: البريد الإلكتروني
            full_name: الاسم الكامل
            role: الدور
            is_active: حالة النشاط
            last_login: آخر تسجيل دخول
            created_at: تاريخ الإنشاء
            updated_at: تاريخ التحديث
        """
        self.id = user_id
        self.username = username
        self.password_hash = password_hash
        self.email = email
        self.full_name = full_name
        self.role = role
        self.is_active = is_active
        self.last_login = last_login
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()
        self.logger = logging.getLogger(__name__)
    
    @classmethod
    def create(cls, username: str, password: str, full_name: str, 
               role: str, email: str = None) -> 'User':
        """
        إنشاء مستخدم جديد
        
        Args:
            username: اسم المستخدم
            password: كلمة المرور
            full_name: الاسم الكامل
            role: الدور
            email: البريد الإلكتروني
            
        Returns:
            User: المستخدم الجديد
        """
        # التحقق من صحة البيانات
        if not username or not password or not full_name or not role:
            raise ValueError("جميع الحقول الأساسية مطلوبة")
        
        if role not in cls.ROLES:
            raise ValueError(f"الدور غير صحيح. الأدوار المتاحة: {list(cls.ROLES.keys())}")
        
        # التحقق من عدم وجود اسم المستخدم مسبقاً
        if cls.get_by_username(username):
            raise ValueError("اسم المستخدم موجود مسبقاً")
        
        # تشفير كلمة المرور
        password_hash = cls.hash_password(password)
        
        # إدراج في قاعدة البيانات
        user_data = {
            'username': username,
            'password_hash': password_hash,
            'email': email,
            'full_name': full_name,
            'role': role,
            'is_active': True,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
        
        user_id = db_handler.insert('users', user_data)
        
        # إنشاء كائن المستخدم
        user = cls(
            user_id=user_id,
            username=username,
            password_hash=password_hash,
            email=email,
            full_name=full_name,
            role=role,
            is_active=True
        )
        
        user.logger.info(f"تم إنشاء مستخدم جديد: {username}")
        return user
    
    @classmethod
    def get_by_id(cls, user_id: int) -> Optional['User']:
        """الحصول على مستخدم بالمعرف"""
        query = "SELECT * FROM users WHERE id = ?"
        row = db_handler.fetch_one(query, (user_id,))
        
        if row:
            return cls._from_row(row)
        return None
    
    @classmethod
    def get_by_username(cls, username: str) -> Optional['User']:
        """الحصول على مستخدم باسم المستخدم"""
        query = "SELECT * FROM users WHERE username = ?"
        row = db_handler.fetch_one(query, (username,))
        
        if row:
            return cls._from_row(row)
        return None
    
    @classmethod
    def get_by_email(cls, email: str) -> Optional['User']:
        """الحصول على مستخدم بالبريد الإلكتروني"""
        query = "SELECT * FROM users WHERE email = ?"
        row = db_handler.fetch_one(query, (email,))
        
        if row:
            return cls._from_row(row)
        return None
    
    @classmethod
    def get_all(cls, role: str = None, is_active: bool = None) -> list:
        """الحصول على جميع المستخدمين مع إمكانية التصفية"""
        query = "SELECT * FROM users WHERE 1=1"
        params = []
        
        if role:
            query += " AND role = ?"
            params.append(role)
        
        if is_active is not None:
            query += " AND is_active = ?"
            params.append(is_active)
        
        query += " ORDER BY created_at DESC"
        
        rows = db_handler.fetch_all(query, tuple(params) if params else None)
        return [cls._from_row(row) for row in rows]
    
    @classmethod
    def authenticate(cls, username: str, password: str) -> Optional['User']:
        """
        التحقق من صحة بيانات تسجيل الدخول
        
        Args:
            username: اسم المستخدم
            password: كلمة المرور
            
        Returns:
            User: المستخدم إذا كانت البيانات صحيحة، None إذا كانت خاطئة
        """
        user = cls.get_by_username(username)
        
        if user and user.is_active and cls.verify_password(password, user.password_hash):
            # تحديث آخر تسجيل دخول
            user.update_last_login()
            return user
        
        return None
    
    @staticmethod
    def hash_password(password: str) -> str:
        """تشفير كلمة المرور"""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    @staticmethod
    def verify_password(password: str, password_hash: str) -> bool:
        """التحقق من كلمة المرور"""
        return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
    
    def update_password(self, new_password: str) -> bool:
        """تحديث كلمة المرور"""
        try:
            new_hash = self.hash_password(new_password)
            
            data = {
                'password_hash': new_hash,
                'updated_at': datetime.now()
            }
            
            rows_affected = db_handler.update('users', data, 'id = ?', (self.id,))
            
            if rows_affected > 0:
                self.password_hash = new_hash
                self.updated_at = datetime.now()
                self.logger.info(f"تم تحديث كلمة المرور للمستخدم: {self.username}")
                return True
            
            return False
        except Exception as e:
            self.logger.error(f"خطأ في تحديث كلمة المرور: {e}")
            return False
    
    def update_last_login(self):
        """تحديث آخر تسجيل دخول"""
        try:
            now = datetime.now()
            data = {'last_login': now}
            
            db_handler.update('users', data, 'id = ?', (self.id,))
            self.last_login = now
        except Exception as e:
            self.logger.error(f"خطأ في تحديث آخر تسجيل دخول: {e}")
    
    def update(self, **kwargs) -> bool:
        """تحديث بيانات المستخدم"""
        try:
            # الحقول المسموح بتحديثها
            allowed_fields = ['email', 'full_name', 'role', 'is_active']
            
            data = {}
            for key, value in kwargs.items():
                if key in allowed_fields:
                    data[key] = value
                    setattr(self, key, value)
            
            if data:
                data['updated_at'] = datetime.now()
                rows_affected = db_handler.update('users', data, 'id = ?', (self.id,))
                
                if rows_affected > 0:
                    self.updated_at = datetime.now()
                    self.logger.info(f"تم تحديث بيانات المستخدم: {self.username}")
                    return True
            
            return False
        except Exception as e:
            self.logger.error(f"خطأ في تحديث بيانات المستخدم: {e}")
            return False
    
    def delete(self) -> bool:
        """حذف المستخدم"""
        try:
            rows_affected = db_handler.delete('users', 'id = ?', (self.id,))
            
            if rows_affected > 0:
                self.logger.info(f"تم حذف المستخدم: {self.username}")
                return True
            
            return False
        except Exception as e:
            self.logger.error(f"خطأ في حذف المستخدم: {e}")
            return False
    
    def has_permission(self, permission: str) -> bool:
        """التحقق من الصلاحيات"""
        # صلاحيات المدير
        if self.role == 'admin':
            return True
        
        # صلاحيات المعلم
        if self.role == 'teacher':
            teacher_permissions = [
                'view_students', 'view_groups', 'manage_attendance',
                'view_courses', 'view_reports'
            ]
            return permission in teacher_permissions
        
        # صلاحيات الموظف
        if self.role == 'employee':
            employee_permissions = [
                'view_students', 'manage_students', 'view_payments',
                'manage_payments', 'view_reports'
            ]
            return permission in employee_permissions
        
        # صلاحيات الطالب
        if self.role == 'student':
            student_permissions = ['view_profile', 'view_attendance', 'view_payments']
            return permission in student_permissions
        
        return False
    
    @classmethod
    def _from_row(cls, row) -> 'User':
        """إنشاء كائن مستخدم من سجل قاعدة البيانات"""
        return cls(
            user_id=row['id'],
            username=row['username'],
            password_hash=row['password_hash'],
            email=row['email'],
            full_name=row['full_name'],
            role=row['role'],
            is_active=bool(row['is_active']),
            last_login=datetime.fromisoformat(row['last_login']) if row['last_login'] else None,
            created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
            updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل المستخدم إلى قاموس"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'full_name': self.full_name,
            'role': self.role,
            'role_name': self.ROLES.get(self.role, self.role),
            'is_active': self.is_active,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __str__(self):
        return f"User(id={self.id}, username='{self.username}', role='{self.role}')"
    
    def __repr__(self):
        return self.__str__()
