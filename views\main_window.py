"""
النافذة الرئيسية - Main Window
"""

import logging
from datetime import datetime
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QPushButton, QFrame, QMenuBar, QMenu, QStatusBar, QToolBar,
    QStackedWidget, QSplitter, QTreeWidget, QTreeWidgetItem,
    QMessageBox, QApplication
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QAction, QIcon, QFont, QPixmap

from models.user import User
from utils.logger import activity_logger

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    logout_requested = pyqtSignal()
    
    def __init__(self, user: User):
        super().__init__()
        self.user = user
        self.logger = logging.getLogger(__name__)
        
        # المتغيرات
        self.current_view = None
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        
        self.init_ui()
        self.setup_menu()
        self.setup_toolbar()
        self.setup_status_bar()
        self.setup_styles()
        
        # بدء مؤقت تحديث الحالة
        self.status_timer.start(60000)  # كل دقيقة
        
        self.logger.info(f"تم فتح النافذة الرئيسية للمستخدم: {user.username}")
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle(f"نظام إدارة الأكاديمية التعليمية - {self.user.full_name}")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(1000, 600)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # إنشاء المقسم الرئيسي
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # الشريط الجانبي
        self.sidebar = self.create_sidebar()
        splitter.addWidget(self.sidebar)
        
        # منطقة المحتوى الرئيسي
        self.content_area = self.create_content_area()
        splitter.addWidget(self.content_area)
        
        # تعيين نسب المقسم
        splitter.setSizes([250, 950])
        splitter.setCollapsible(0, False)
        splitter.setCollapsible(1, False)
        
        main_layout.addWidget(splitter)
        
        # عرض لوحة التحكم افتراضياً
        self.show_dashboard()
    
    def create_sidebar(self) -> QWidget:
        """إنشاء الشريط الجانبي"""
        sidebar = QFrame()
        sidebar.setFrameStyle(QFrame.Shape.StyledPanel)
        sidebar.setFixedWidth(250)
        
        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)
        
        # معلومات المستخدم
        user_info = self.create_user_info()
        layout.addWidget(user_info)
        
        # فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(separator)
        
        # قائمة التنقل
        self.navigation_tree = self.create_navigation_tree()
        layout.addWidget(self.navigation_tree)
        
        # مساحة فارغة
        layout.addStretch()
        
        # زر تسجيل الخروج
        logout_button = QPushButton("تسجيل الخروج")
        logout_button.clicked.connect(self.on_logout_clicked)
        logout_button.setObjectName("logout_button")
        layout.addWidget(logout_button)
        
        return sidebar
    
    def create_user_info(self) -> QWidget:
        """إنشاء معلومات المستخدم"""
        user_frame = QFrame()
        user_frame.setFrameStyle(QFrame.Shape.Box)
        
        layout = QVBoxLayout(user_frame)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # صورة المستخدم (افتراضية)
        avatar_label = QLabel()
        avatar_label.setFixedSize(60, 60)
        avatar_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        avatar_label.setStyleSheet("""
            QLabel {
                border: 2px solid #3498db;
                border-radius: 30px;
                background-color: #ecf0f1;
                font-size: 24px;
                font-weight: bold;
                color: #3498db;
            }
        """)
        # عرض الحرف الأول من الاسم
        avatar_label.setText(self.user.full_name[0].upper() if self.user.full_name else "U")
        
        # اسم المستخدم
        name_label = QLabel(self.user.full_name)
        name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        name_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50;")
        
        # دور المستخدم
        role_label = QLabel(User.ROLES.get(self.user.role, self.user.role))
        role_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        role_label.setStyleSheet("color: #7f8c8d; font-size: 12px;")
        
        layout.addWidget(avatar_label, alignment=Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(name_label)
        layout.addWidget(role_label)
        
        return user_frame
    
    def create_navigation_tree(self) -> QTreeWidget:
        """إنشاء شجرة التنقل"""
        tree = QTreeWidget()
        tree.setHeaderHidden(True)
        tree.setRootIsDecorated(True)
        tree.setIndentation(20)
        
        # إضافة العناصر حسب صلاحيات المستخدم
        self.add_navigation_items(tree)
        
        # ربط الإشارات
        tree.itemClicked.connect(self.on_navigation_item_clicked)
        
        return tree
    
    def add_navigation_items(self, tree: QTreeWidget):
        """إضافة عناصر التنقل"""
        # لوحة التحكم (متاحة للجميع)
        dashboard_item = QTreeWidgetItem(tree, ["لوحة التحكم"])
        dashboard_item.setData(0, Qt.ItemDataRole.UserRole, "dashboard")
        
        # إدارة الطلاب
        if self.user.has_permission('view_students'):
            students_item = QTreeWidgetItem(tree, ["إدارة الطلاب"])
            students_item.setData(0, Qt.ItemDataRole.UserRole, "students")
            
            if self.user.has_permission('manage_students'):
                QTreeWidgetItem(students_item, ["إضافة طالب"]).setData(0, Qt.ItemDataRole.UserRole, "add_student")
                QTreeWidgetItem(students_item, ["قائمة الطلاب"]).setData(0, Qt.ItemDataRole.UserRole, "list_students")
        
        # إدارة المعلمين (للمدير فقط)
        if self.user.role == 'admin':
            teachers_item = QTreeWidgetItem(tree, ["إدارة المعلمين"])
            teachers_item.setData(0, Qt.ItemDataRole.UserRole, "teachers")
            
            QTreeWidgetItem(teachers_item, ["إضافة معلم"]).setData(0, Qt.ItemDataRole.UserRole, "add_teacher")
            QTreeWidgetItem(teachers_item, ["قائمة المعلمين"]).setData(0, Qt.ItemDataRole.UserRole, "list_teachers")
        
        # إدارة الكورسات
        if self.user.has_permission('view_courses'):
            courses_item = QTreeWidgetItem(tree, ["إدارة الكورسات"])
            courses_item.setData(0, Qt.ItemDataRole.UserRole, "courses")
            
            if self.user.role == 'admin':
                QTreeWidgetItem(courses_item, ["إضافة كورس"]).setData(0, Qt.ItemDataRole.UserRole, "add_course")
            QTreeWidgetItem(courses_item, ["قائمة الكورسات"]).setData(0, Qt.ItemDataRole.UserRole, "list_courses")
        
        # إدارة المجموعات
        if self.user.has_permission('view_groups'):
            groups_item = QTreeWidgetItem(tree, ["إدارة المجموعات"])
            groups_item.setData(0, Qt.ItemDataRole.UserRole, "groups")
            
            if self.user.role in ['admin', 'teacher']:
                QTreeWidgetItem(groups_item, ["إضافة مجموعة"]).setData(0, Qt.ItemDataRole.UserRole, "add_group")
            QTreeWidgetItem(groups_item, ["قائمة المجموعات"]).setData(0, Qt.ItemDataRole.UserRole, "list_groups")
        
        # الحضور والغياب
        if self.user.has_permission('manage_attendance'):
            attendance_item = QTreeWidgetItem(tree, ["الحضور والغياب"])
            attendance_item.setData(0, Qt.ItemDataRole.UserRole, "attendance")
            
            QTreeWidgetItem(attendance_item, ["تسجيل الحضور"]).setData(0, Qt.ItemDataRole.UserRole, "record_attendance")
            QTreeWidgetItem(attendance_item, ["تقارير الحضور"]).setData(0, Qt.ItemDataRole.UserRole, "attendance_reports")
        
        # النظام المالي
        if self.user.has_permission('view_payments'):
            finance_item = QTreeWidgetItem(tree, ["النظام المالي"])
            finance_item.setData(0, Qt.ItemDataRole.UserRole, "finance")
            
            if self.user.has_permission('manage_payments'):
                QTreeWidgetItem(finance_item, ["إضافة دفعة"]).setData(0, Qt.ItemDataRole.UserRole, "add_payment")
            QTreeWidgetItem(finance_item, ["المدفوعات"]).setData(0, Qt.ItemDataRole.UserRole, "list_payments")
            
            if self.user.role == 'admin':
                QTreeWidgetItem(finance_item, ["رواتب المعلمين"]).setData(0, Qt.ItemDataRole.UserRole, "teacher_salaries")
        
        # التقارير
        if self.user.has_permission('view_reports'):
            reports_item = QTreeWidgetItem(tree, ["التقارير"])
            reports_item.setData(0, Qt.ItemDataRole.UserRole, "reports")
            
            QTreeWidgetItem(reports_item, ["تقرير الطلاب"]).setData(0, Qt.ItemDataRole.UserRole, "students_report")
            QTreeWidgetItem(reports_item, ["تقرير الحضور"]).setData(0, Qt.ItemDataRole.UserRole, "attendance_report")
            QTreeWidgetItem(reports_item, ["التقرير المالي"]).setData(0, Qt.ItemDataRole.UserRole, "financial_report")
        
        # الإعدادات (للمدير فقط)
        if self.user.role == 'admin':
            settings_item = QTreeWidgetItem(tree, ["الإعدادات"])
            settings_item.setData(0, Qt.ItemDataRole.UserRole, "settings")
            
            QTreeWidgetItem(settings_item, ["إعدادات النظام"]).setData(0, Qt.ItemDataRole.UserRole, "system_settings")
            QTreeWidgetItem(settings_item, ["إدارة المستخدمين"]).setData(0, Qt.ItemDataRole.UserRole, "user_management")
            QTreeWidgetItem(settings_item, ["النسخ الاحتياطية"]).setData(0, Qt.ItemDataRole.UserRole, "backup_management")
        
        # توسيع العناصر الرئيسية
        tree.expandAll()
    
    def create_content_area(self) -> QWidget:
        """إنشاء منطقة المحتوى"""
        content_widget = QStackedWidget()
        content_widget.setObjectName("content_area")
        
        return content_widget
    
    def setup_menu(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu("ملف")
        
        # إعدادات
        settings_action = QAction("الإعدادات", self)
        settings_action.triggered.connect(self.show_settings)
        file_menu.addAction(settings_action)
        
        file_menu.addSeparator()
        
        # تسجيل الخروج
        logout_action = QAction("تسجيل الخروج", self)
        logout_action.triggered.connect(self.on_logout_clicked)
        file_menu.addAction(logout_action)
        
        file_menu.addSeparator()
        
        # خروج
        exit_action = QAction("خروج", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة العرض
        view_menu = menubar.addMenu("عرض")
        
        # لوحة التحكم
        dashboard_action = QAction("لوحة التحكم", self)
        dashboard_action.triggered.connect(self.show_dashboard)
        view_menu.addAction(dashboard_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        # حول
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setMovable(False)
        
        # لوحة التحكم
        dashboard_action = QAction("لوحة التحكم", self)
        dashboard_action.triggered.connect(self.show_dashboard)
        toolbar.addAction(dashboard_action)
        
        toolbar.addSeparator()
        
        # إضافة طالب (إذا كان مسموحاً)
        if self.user.has_permission('manage_students'):
            add_student_action = QAction("إضافة طالب", self)
            add_student_action.triggered.connect(lambda: self.show_view("add_student"))
            toolbar.addAction(add_student_action)
        
        # تسجيل الحضور (إذا كان مسموحاً)
        if self.user.has_permission('manage_attendance'):
            attendance_action = QAction("تسجيل الحضور", self)
            attendance_action.triggered.connect(lambda: self.show_view("record_attendance"))
            toolbar.addAction(attendance_action)
    
    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        status_bar = self.statusBar()
        
        # معلومات المستخدم
        user_info = f"المستخدم: {self.user.full_name} ({User.ROLES.get(self.user.role, self.user.role)})"
        status_bar.addWidget(QLabel(user_info))
        
        # فاصل
        status_bar.addPermanentWidget(QLabel(" | "))
        
        # الوقت الحالي
        self.time_label = QLabel()
        self.update_status()
        status_bar.addPermanentWidget(self.time_label)
    
    def setup_styles(self):
        """إعداد الأنماط"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ecf0f1;
            }
            
            QFrame {
                background-color: white;
                border: 1px solid #bdc3c7;
            }
            
            QTreeWidget {
                background-color: white;
                border: none;
                outline: none;
                font-size: 12px;
            }
            
            QTreeWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            
            QTreeWidget::item:hover {
                background-color: #e8f4fd;
            }
            
            QTreeWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            
            QPushButton#logout_button {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            }
            
            QPushButton#logout_button:hover {
                background-color: #c0392b;
            }
            
            QStackedWidget#content_area {
                background-color: white;
                border: none;
            }
            
            QMenuBar {
                background-color: #34495e;
                color: white;
                border: none;
                padding: 4px;
            }
            
            QMenuBar::item {
                background-color: transparent;
                padding: 8px 12px;
            }
            
            QMenuBar::item:selected {
                background-color: #2c3e50;
            }
            
            QMenu {
                background-color: white;
                border: 1px solid #bdc3c7;
                color: #2c3e50;
            }
            
            QMenu::item {
                padding: 8px 20px;
            }
            
            QMenu::item:selected {
                background-color: #3498db;
                color: white;
            }
            
            QToolBar {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                spacing: 3px;
                padding: 5px;
            }
            
            QStatusBar {
                background-color: #f8f9fa;
                border-top: 1px solid #dee2e6;
                color: #6c757d;
            }
        """)
    
    def on_navigation_item_clicked(self, item: QTreeWidgetItem, column: int):
        """معالج النقر على عنصر التنقل"""
        view_name = item.data(0, Qt.ItemDataRole.UserRole)
        if view_name:
            self.show_view(view_name)
    
    def show_view(self, view_name: str):
        """عرض واجهة معينة"""
        try:
            # تسجيل النشاط
            activity_logger.log_user_action(
                self.user.id, 
                self.user.username, 
                f"عرض واجهة: {view_name}"
            )
            
            # هنا سيتم إضافة منطق عرض الواجهات المختلفة
            # مؤقتاً سنعرض رسالة
            self.show_placeholder(view_name)
            
        except Exception as e:
            self.logger.error(f"خطأ في عرض الواجهة {view_name}: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في عرض الواجهة:\n{e}")
    
    def show_placeholder(self, view_name: str):
        """عرض واجهة مؤقتة"""
        placeholder = QLabel(f"واجهة {view_name}\n\nقيد التطوير...")
        placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder.setStyleSheet("""
            QLabel {
                font-size: 18px;
                color: #7f8c8d;
                background-color: white;
            }
        """)
        
        # إزالة الواجهة السابقة وإضافة الجديدة
        content_area = self.centralWidget().findChild(QStackedWidget, "content_area")
        if content_area:
            # مسح الواجهات السابقة
            while content_area.count() > 0:
                widget = content_area.widget(0)
                content_area.removeWidget(widget)
                widget.deleteLater()
            
            content_area.addWidget(placeholder)
            content_area.setCurrentWidget(placeholder)
    
    def show_dashboard(self):
        """عرض لوحة التحكم"""
        self.show_view("dashboard")
    
    def show_settings(self):
        """عرض الإعدادات"""
        if self.user.role == 'admin':
            self.show_view("system_settings")
        else:
            QMessageBox.warning(self, "تحذير", "ليس لديك صلاحية للوصول إلى الإعدادات")
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = """
        <h2>نظام إدارة الأكاديمية التعليمية</h2>
        <p><b>الإصدار:</b> 1.0.0</p>
        <p><b>تاريخ الإصدار:</b> 2024</p>
        <p><b>الوصف:</b> نظام متكامل لإدارة الأكاديميات التعليمية</p>
        <p><b>المطور:</b> فريق التطوير</p>
        <hr>
        <p>جميع الحقوق محفوظة © 2024</p>
        """
        
        QMessageBox.about(self, "حول البرنامج", about_text)
    
    def update_status(self):
        """تحديث شريط الحالة"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(f"الوقت: {current_time}")
    
    def on_logout_clicked(self):
        """معالج النقر على تسجيل الخروج"""
        reply = QMessageBox.question(
            self, 
            "تأكيد تسجيل الخروج",
            "هل أنت متأكد من رغبتك في تسجيل الخروج؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # تسجيل النشاط
            activity_logger.log_logout(self.user.id, self.user.username)
            
            # إرسال إشارة تسجيل الخروج
            self.logout_requested.emit()
    
    def closeEvent(self, event):
        """معالج إغلاق النافذة"""
        reply = QMessageBox.question(
            self,
            "تأكيد الإغلاق",
            "هل أنت متأكد من رغبتك في إغلاق البرنامج؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # إيقاف المؤقت
            if self.status_timer.isActive():
                self.status_timer.stop()
            
            # تسجيل النشاط
            activity_logger.log_user_action(
                self.user.id, 
                self.user.username, 
                "إغلاق البرنامج"
            )
            
            event.accept()
        else:
            event.ignore()
