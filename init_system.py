#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تهيئة النظام الأولية - System Initialization
إعداد النظام للمرة الأولى مع بيانات تجريبية
"""

import sys
import os
from pathlib import Path
from datetime import date, datetime
from decimal import Decimal

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def init_database():
    """تهيئة قاعدة البيانات"""
    print("🗄️ تهيئة قاعدة البيانات...")
    
    try:
        from database.db_handler import db_handler
        
        # حذف قاعدة البيانات الموجودة إذا كانت موجودة
        db_path = "database/academy_db.sqlite"
        if os.path.exists(db_path):
            os.remove(db_path)
            print("✅ تم حذف قاعدة البيانات القديمة")
        
        # تهيئة قاعدة البيانات الجديدة
        db_handler.initialize_database()
        print("✅ تم إنشاء قاعدة البيانات الجديدة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
        return False

def create_sample_data():
    """إنشاء بيانات تجريبية"""
    print("\n📊 إنشاء بيانات تجريبية...")
    
    try:
        from models.user import User
        from models.student import Student
        from models.teacher import Teacher
        from models.course import Course
        from models.group import Group
        from models.finance import Payment
        
        # إنشاء مستخدمين إضافيين
        print("👥 إنشاء مستخدمين...")
        
        # معلم
        teacher_user = User.create(
            username="teacher1",
            password="teacher123",
            full_name="أحمد محمد المعلم",
            role="teacher",
            email="<EMAIL>"
        )
        print(f"✅ تم إنشاء مستخدم معلم: {teacher_user.username}")
        
        # موظف
        staff_user = User.create(
            username="staff1",
            password="staff123",
            full_name="فاطمة علي الموظفة",
            role="staff",
            email="<EMAIL>"
        )
        print(f"✅ تم إنشاء مستخدم موظف: {staff_user.username}")
        
        # إنشاء معلمين
        print("\n👨‍🏫 إنشاء معلمين...")
        
        teachers_data = [
            {
                'first_name': 'أحمد',
                'last_name': 'محمد',
                'email': '<EMAIL>',
                'phone': '0501111111',
                'specialization': 'الرياضيات',
                'qualification': 'ماجستير رياضيات',
                'salary': Decimal('8000.00'),
                'user_id': teacher_user.id
            },
            {
                'first_name': 'سارة',
                'last_name': 'خالد',
                'email': '<EMAIL>',
                'phone': '0502222222',
                'specialization': 'اللغة العربية',
                'qualification': 'بكالوريوس أدب عربي',
                'salary': Decimal('7500.00')
            },
            {
                'first_name': 'محمد',
                'last_name': 'عبدالله',
                'email': '<EMAIL>',
                'phone': '0503333333',
                'specialization': 'العلوم',
                'qualification': 'ماجستير كيمياء',
                'salary': Decimal('7800.00')
            }
        ]
        
        teachers = []
        for teacher_data in teachers_data:
            teacher = Teacher.create(**teacher_data)
            teachers.append(teacher)
            print(f"✅ تم إنشاء المعلم: {teacher.full_name} ({teacher.teacher_code})")
        
        # إنشاء كورسات
        print("\n📚 إنشاء كورسات...")
        
        courses_data = [
            {
                'name': 'أساسيات الرياضيات',
                'description': 'كورس تأسيسي في الرياضيات للمرحلة الابتدائية',
                'duration_hours': 40,
                'price': Decimal('500.00'),
                'category': 'رياضيات',
                'level': 'beginner',
                'max_students': 20
            },
            {
                'name': 'اللغة العربية المتقدمة',
                'description': 'كورس متقدم في اللغة العربية والنحو',
                'duration_hours': 60,
                'price': Decimal('750.00'),
                'category': 'لغة عربية',
                'level': 'advanced',
                'max_students': 15
            },
            {
                'name': 'العلوم العامة',
                'description': 'مقدمة في العلوم الطبيعية',
                'duration_hours': 50,
                'price': Decimal('600.00'),
                'category': 'علوم',
                'level': 'intermediate',
                'max_students': 18
            },
            {
                'name': 'الرياضيات المتقدمة',
                'description': 'كورس متقدم في الجبر والهندسة',
                'duration_hours': 80,
                'price': Decimal('900.00'),
                'category': 'رياضيات',
                'level': 'advanced',
                'max_students': 12
            }
        ]
        
        courses = []
        for course_data in courses_data:
            course = Course.create(**course_data)
            courses.append(course)
            print(f"✅ تم إنشاء الكورس: {course.name} ({course.course_code})")
        
        # إنشاء مجموعات
        print("\n👥 إنشاء مجموعات...")
        
        groups_data = [
            {
                'name': 'مجموعة الرياضيات الصباحية',
                'course_id': courses[0].id,
                'teacher_id': teachers[0].id,
                'start_date': date(2024, 1, 15),
                'end_date': date(2024, 3, 15),
                'schedule_days': 'sunday,tuesday,thursday',
                'start_time': '09:00',
                'end_time': '11:00',
                'max_students': 20,
                'classroom': 'قاعة A1'
            },
            {
                'name': 'مجموعة العربية المسائية',
                'course_id': courses[1].id,
                'teacher_id': teachers[1].id,
                'start_date': date(2024, 1, 20),
                'end_date': date(2024, 4, 20),
                'schedule_days': 'monday,wednesday,friday',
                'start_time': '16:00',
                'end_time': '18:00',
                'max_students': 15,
                'classroom': 'قاعة B2'
            },
            {
                'name': 'مجموعة العلوم الصباحية',
                'course_id': courses[2].id,
                'teacher_id': teachers[2].id,
                'start_date': date(2024, 2, 1),
                'end_date': date(2024, 4, 1),
                'schedule_days': 'sunday,tuesday',
                'start_time': '10:00',
                'end_time': '12:00',
                'max_students': 18,
                'classroom': 'مختبر العلوم'
            }
        ]
        
        groups = []
        for group_data in groups_data:
            group = Group.create(**group_data)
            groups.append(group)
            print(f"✅ تم إنشاء المجموعة: {group.name} ({group.group_code})")
        
        # إنشاء طلاب
        print("\n🎓 إنشاء طلاب...")
        
        students_data = [
            {
                'first_name': 'علي',
                'last_name': 'أحمد',
                'email': '<EMAIL>',
                'phone': '0551111111',
                'date_of_birth': date(2010, 5, 15),
                'gender': 'male',
                'guardian_name': 'أحمد علي',
                'guardian_phone': '0501111111'
            },
            {
                'first_name': 'فاطمة',
                'last_name': 'محمد',
                'email': '<EMAIL>',
                'phone': '0552222222',
                'date_of_birth': date(2009, 8, 20),
                'gender': 'female',
                'guardian_name': 'محمد فاطمة',
                'guardian_phone': '0502222222'
            },
            {
                'first_name': 'خالد',
                'last_name': 'سعد',
                'email': '<EMAIL>',
                'phone': '0553333333',
                'date_of_birth': date(2011, 3, 10),
                'gender': 'male',
                'guardian_name': 'سعد خالد',
                'guardian_phone': '0503333333'
            },
            {
                'first_name': 'نورا',
                'last_name': 'عبدالله',
                'email': '<EMAIL>',
                'phone': '0554444444',
                'date_of_birth': date(2010, 12, 5),
                'gender': 'female',
                'guardian_name': 'عبدالله نورا',
                'guardian_phone': '0504444444'
            },
            {
                'first_name': 'يوسف',
                'last_name': 'إبراهيم',
                'email': '<EMAIL>',
                'phone': '0555555555',
                'date_of_birth': date(2009, 7, 25),
                'gender': 'male',
                'guardian_name': 'إبراهيم يوسف',
                'guardian_phone': '0505555555'
            }
        ]
        
        students = []
        for student_data in students_data:
            student = Student.create(**student_data)
            students.append(student)
            print(f"✅ تم إنشاء الطالب: {student.full_name} ({student.student_code})")
        
        # تسجيل الطلاب في المجموعات
        print("\n📝 تسجيل الطلاب في المجموعات...")
        
        # تسجيل الطلاب في مجموعة الرياضيات
        for i, student in enumerate(students[:3]):
            groups[0].add_student(student.id)
            print(f"✅ تم تسجيل {student.full_name} في {groups[0].name}")
        
        # تسجيل الطلاب في مجموعة العربية
        for i, student in enumerate(students[1:4]):
            groups[1].add_student(student.id)
            print(f"✅ تم تسجيل {student.full_name} في {groups[1].name}")
        
        # تسجيل الطلاب في مجموعة العلوم
        for i, student in enumerate(students[2:]):
            groups[2].add_student(student.id)
            print(f"✅ تم تسجيل {student.full_name} في {groups[2].name}")
        
        # إنشاء مدفوعات تجريبية
        print("\n💰 إنشاء مدفوعات تجريبية...")
        
        for i, student in enumerate(students):
            # دفعة رسوم دراسية
            payment = Payment.create(
                student_id=student.id,
                amount=Decimal('500.00'),
                payment_type='tuition',
                payment_method='cash',
                payment_date=date.today(),
                status='paid'
            )
            print(f"✅ تم إنشاء دفعة للطالب {student.full_name}: {payment.receipt_number}")
        
        print("\n✅ تم إنشاء جميع البيانات التجريبية بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🎓 تهيئة نظام إدارة الأكاديمية التعليمية")
    print("=" * 50)
    
    # تهيئة قاعدة البيانات
    if not init_database():
        print("❌ فشل في تهيئة قاعدة البيانات")
        return 1
    
    # إنشاء البيانات التجريبية
    if not create_sample_data():
        print("❌ فشل في إنشاء البيانات التجريبية")
        return 1
    
    print("\n" + "=" * 50)
    print("🎉 تم إعداد النظام بنجاح!")
    print("\nبيانات تسجيل الدخول:")
    print("المدير:")
    print("  اسم المستخدم: admin")
    print("  كلمة المرور: admin123")
    print("\nالمعلم:")
    print("  اسم المستخدم: teacher1")
    print("  كلمة المرور: teacher123")
    print("\nالموظف:")
    print("  اسم المستخدم: staff1")
    print("  كلمة المرور: staff123")
    
    print("\nيمكنك الآن تشغيل النظام:")
    print("  python main.py")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف التهيئة")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ فادح: {e}")
        sys.exit(1)
