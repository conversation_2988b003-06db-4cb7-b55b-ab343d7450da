"""
تحكم الكورسات - Course Controller
"""

import logging
from typing import List, Dict, Any, Optional
from decimal import Decimal

from models.course import Course
from models.user import User
from utils.validators import DataValidator
from utils.logger import activity_logger

class CourseController:
    """تحكم عمليات الكورسات"""
    
    def __init__(self, current_user: User):
        """
        تهيئة تحكم الكورسات
        
        Args:
            current_user: المستخدم الحالي
        """
        self.current_user = current_user
        self.logger = logging.getLogger(__name__)
    
    def create_course(self, course_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        إنشاء كورس جديد
        
        Args:
            course_data: بيانات الكورس
            
        Returns:
            Dict: نتيجة العملية
        """
        try:
            # التحقق من الصلاحيات (المدير فقط)
            if self.current_user.role != 'admin':
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لإضافة الكورسات'
                }
            
            # التحقق من صحة البيانات
            if not course_data.get('name'):
                return {
                    'success': False,
                    'message': 'اسم الكورس مطلوب'
                }
            
            # تحويل السعر إلى Decimal إذا كان موجوداً
            if 'price' in course_data and course_data['price']:
                if not DataValidator.validate_amount(course_data['price']):
                    return {
                        'success': False,
                        'message': 'السعر غير صحيح'
                    }
                course_data['price'] = Decimal(str(course_data['price']))
            
            # التحقق من المستوى
            if 'level' in course_data and course_data['level'] not in Course.LEVELS:
                return {
                    'success': False,
                    'message': f"المستوى غير صحيح. المستويات المتاحة: {list(Course.LEVELS.keys())}"
                }
            
            # إنشاء الكورس
            course = Course.create(**course_data)
            
            # تسجيل النشاط
            activity_logger.log_user_action(
                self.current_user.id,
                self.current_user.username,
                'إضافة كورس',
                f"تم إضافة الكورس: {course.name} ({course.course_code})"
            )
            
            return {
                'success': True,
                'message': 'تم إنشاء الكورس بنجاح',
                'course': course.to_dict()
            }
            
        except ValueError as e:
            return {
                'success': False,
                'message': str(e)
            }
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الكورس: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def get_course(self, course_id: int) -> Dict[str, Any]:
        """
        الحصول على بيانات كورس
        
        Args:
            course_id: معرف الكورس
            
        Returns:
            Dict: بيانات الكورس
        """
        try:
            # التحقق من الصلاحيات
            if not self.current_user.has_permission('view_courses'):
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لعرض الكورسات'
                }
            
            course = Course.get_by_id(course_id)
            if not course:
                return {
                    'success': False,
                    'message': 'الكورس غير موجود'
                }
            
            return {
                'success': True,
                'course': course.to_dict()
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الكورس: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def update_course(self, course_id: int, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        تحديث بيانات كورس
        
        Args:
            course_id: معرف الكورس
            update_data: البيانات المحدثة
            
        Returns:
            Dict: نتيجة العملية
        """
        try:
            # التحقق من الصلاحيات (المدير فقط)
            if self.current_user.role != 'admin':
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لتعديل الكورسات'
                }
            
            course = Course.get_by_id(course_id)
            if not course:
                return {
                    'success': False,
                    'message': 'الكورس غير موجود'
                }
            
            # تحويل السعر إلى Decimal إذا كان موجوداً
            if 'price' in update_data and update_data['price']:
                if not DataValidator.validate_amount(update_data['price']):
                    return {
                        'success': False,
                        'message': 'السعر غير صحيح'
                    }
                update_data['price'] = Decimal(str(update_data['price']))
            
            # التحقق من المستوى
            if 'level' in update_data and update_data['level'] not in Course.LEVELS:
                return {
                    'success': False,
                    'message': f"المستوى غير صحيح. المستويات المتاحة: {list(Course.LEVELS.keys())}"
                }
            
            # تحديث البيانات
            if course.update(**update_data):
                # تسجيل النشاط
                activity_logger.log_user_action(
                    self.current_user.id,
                    self.current_user.username,
                    'تعديل كورس',
                    f"تم تعديل الكورس: {course.name} ({course.course_code})"
                )
                
                return {
                    'success': True,
                    'message': 'تم تحديث بيانات الكورس بنجاح',
                    'course': course.to_dict()
                }
            else:
                return {
                    'success': False,
                    'message': 'فشل في تحديث البيانات'
                }
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديث الكورس: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def delete_course(self, course_id: int) -> Dict[str, Any]:
        """
        حذف كورس
        
        Args:
            course_id: معرف الكورس
            
        Returns:
            Dict: نتيجة العملية
        """
        try:
            # التحقق من الصلاحيات (المدير فقط)
            if self.current_user.role != 'admin':
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لحذف الكورسات'
                }
            
            course = Course.get_by_id(course_id)
            if not course:
                return {
                    'success': False,
                    'message': 'الكورس غير موجود'
                }
            
            course_name = course.name
            course_code = course.course_code
            
            if course.delete():
                # تسجيل النشاط
                activity_logger.log_user_action(
                    self.current_user.id,
                    self.current_user.username,
                    'حذف كورس',
                    f"تم حذف الكورس: {course_name} ({course_code})"
                )
                
                return {
                    'success': True,
                    'message': 'تم حذف الكورس بنجاح'
                }
            else:
                return {
                    'success': False,
                    'message': 'فشل في حذف الكورس'
                }
            
        except ValueError as e:
            return {
                'success': False,
                'message': str(e)
            }
        except Exception as e:
            self.logger.error(f"خطأ في حذف الكورس: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def search_courses(self, search_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        البحث عن الكورسات
        
        Args:
            search_params: معاملات البحث
            
        Returns:
            Dict: نتائج البحث
        """
        try:
            # التحقق من الصلاحيات
            if not self.current_user.has_permission('view_courses'):
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لعرض الكورسات'
                }
            
            courses = Course.search(
                search_term=search_params.get('search_term'),
                category=search_params.get('category'),
                level=search_params.get('level'),
                is_active=search_params.get('is_active'),
                limit=search_params.get('limit', 50)
            )
            
            return {
                'success': True,
                'courses': [course.to_dict() for course in courses],
                'count': len(courses)
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في البحث عن الكورسات: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def get_course_groups(self, course_id: int) -> Dict[str, Any]:
        """
        الحصول على مجموعات الكورس
        
        Args:
            course_id: معرف الكورس
            
        Returns:
            Dict: مجموعات الكورس
        """
        try:
            course = Course.get_by_id(course_id)
            if not course:
                return {
                    'success': False,
                    'message': 'الكورس غير موجود'
                }
            
            groups = course.get_groups()
            active_groups = course.get_active_groups()
            
            return {
                'success': True,
                'all_groups': groups,
                'active_groups': active_groups
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على مجموعات الكورس: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def get_course_statistics(self, course_id: int) -> Dict[str, Any]:
        """
        الحصول على إحصائيات الكورس
        
        Args:
            course_id: معرف الكورس
            
        Returns:
            Dict: إحصائيات الكورس
        """
        try:
            course = Course.get_by_id(course_id)
            if not course:
                return {
                    'success': False,
                    'message': 'الكورس غير موجود'
                }
            
            statistics = course.get_statistics()
            
            return {
                'success': True,
                'statistics': statistics
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات الكورس: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def get_course_teachers(self, course_id: int) -> Dict[str, Any]:
        """
        الحصول على معلمي الكورس
        
        Args:
            course_id: معرف الكورس
            
        Returns:
            Dict: معلمي الكورس
        """
        try:
            course = Course.get_by_id(course_id)
            if not course:
                return {
                    'success': False,
                    'message': 'الكورس غير موجود'
                }
            
            teachers = course.get_teachers()
            
            return {
                'success': True,
                'teachers': teachers
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على معلمي الكورس: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def get_categories(self) -> Dict[str, Any]:
        """
        الحصول على جميع فئات الكورسات
        
        Returns:
            Dict: فئات الكورسات
        """
        try:
            categories = Course.get_categories()
            
            return {
                'success': True,
                'categories': categories
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الفئات: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
