#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد النظام - System Setup
ملف إعداد سريع لنظام إدارة الأكاديمية التعليمية
"""

import sys
import os
import subprocess
from pathlib import Path

def create_admin_user():
    """إنشاء مستخدم مدير جديد"""
    print("🔐 إعداد مستخدم المدير...")
    
    try:
        # إضافة مسار المشروع
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        
        from models.user import User
        import bcrypt
        
        # طلب بيانات المدير
        print("\nيرجى إدخال بيانات مدير النظام:")
        
        username = input("اسم المستخدم: ").strip()
        if not username:
            username = "admin"
        
        full_name = input("الاسم الكامل: ").strip()
        if not full_name:
            full_name = "مدير النظام"
        
        email = input("البريد الإلكتروني (اختياري): ").strip()
        if not email:
            email = None
        
        # كلمة المرور
        import getpass
        while True:
            password = getpass.getpass("كلمة المرور: ")
            if len(password) >= 6:
                confirm_password = getpass.getpass("تأكيد كلمة المرور: ")
                if password == confirm_password:
                    break
                else:
                    print("❌ كلمات المرور غير متطابقة")
            else:
                print("❌ كلمة المرور يجب أن تكون 6 أحرف على الأقل")
        
        # التحقق من وجود المستخدم
        existing_user = User.get_by_username(username)
        if existing_user:
            print(f"⚠️ المستخدم {username} موجود مسبقاً")
            choice = input("هل تريد تحديث كلمة المرور؟ (y/n): ").lower()
            if choice in ['y', 'yes', 'نعم']:
                existing_user.update_password(password)
                if email:
                    existing_user.update(email=email, full_name=full_name)
                print("✅ تم تحديث بيانات المستخدم")
            return True
        
        # إنشاء مستخدم جديد
        user = User.create(
            username=username,
            password=password,
            full_name=full_name,
            role='admin',
            email=email
        )
        
        print(f"✅ تم إنشاء مستخدم المدير: {username}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدم: {e}")
        return False

def setup_database():
    """إعداد قاعدة البيانات"""
    print("🗄️ إعداد قاعدة البيانات...")
    
    try:
        # إضافة مسار المشروع
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        
        from database.db_handler import db_handler
        
        # حذف قاعدة البيانات الموجودة إذا كانت موجودة
        db_path = "database/academy_db.sqlite"
        if os.path.exists(db_path):
            choice = input("قاعدة البيانات موجودة. هل تريد إعادة إنشائها؟ (y/n): ").lower()
            if choice in ['y', 'yes', 'نعم']:
                os.remove(db_path)
                print("✅ تم حذف قاعدة البيانات القديمة")
            else:
                print("✅ سيتم استخدام قاعدة البيانات الموجودة")
                return True
        
        # تهيئة قاعدة البيانات
        db_handler.initialize_database()
        print("✅ تم إعداد قاعدة البيانات بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    
    try:
        # تحديث pip
        print("تحديث pip...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # تثبيت المتطلبات
        print("تثبيت المكتبات...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        
        print("✅ تم تثبيت جميع المتطلبات بنجاح")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المتطلبات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في التثبيت: {e}")
        return False

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    print("📁 إنشاء المجلدات...")
    
    directories = [
        'logs',
        'exports',
        'database/backup',
        'resources/images',
        'resources/themes',
        'resources/localization'
    ]
    
    try:
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            print(f"✅ {directory}")
        
        print("✅ تم إنشاء جميع المجلدات")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المجلدات: {e}")
        return False

def setup_logging():
    """إعداد نظام السجلات"""
    print("📝 إعداد نظام السجلات...")
    
    try:
        # إضافة مسار المشروع
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        
        from utils.logger import setup_logging
        
        setup_logging()
        print("✅ تم إعداد نظام السجلات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعداد السجلات: {e}")
        return False

def main():
    """الدالة الرئيسية للإعداد"""
    print("🎓 مرحباً بك في إعداد نظام إدارة الأكاديمية التعليمية")
    print("=" * 60)
    
    steps = [
        ("إنشاء المجلدات", create_directories),
        ("تثبيت المتطلبات", install_requirements),
        ("إعداد نظام السجلات", setup_logging),
        ("إعداد قاعدة البيانات", setup_database),
        ("إنشاء مستخدم المدير", create_admin_user),
    ]
    
    print("سيتم تنفيذ الخطوات التالية:")
    for i, (step_name, _) in enumerate(steps, 1):
        print(f"{i}. {step_name}")
    
    print("\nهل تريد المتابعة؟ (y/n): ", end="")
    choice = input().lower()
    
    if choice not in ['y', 'yes', 'نعم']:
        print("تم إلغاء الإعداد")
        return 1
    
    print("\n" + "=" * 60)
    
    # تنفيذ الخطوات
    for step_name, step_func in steps:
        print(f"\n🔄 {step_name}...")
        try:
            if not step_func():
                print(f"❌ فشل في {step_name}")
                return 1
        except Exception as e:
            print(f"❌ خطأ في {step_name}: {e}")
            return 1
    
    print("\n" + "=" * 60)
    print("🎉 تم إعداد النظام بنجاح!")
    print("\nيمكنك الآن تشغيل النظام باستخدام:")
    print("  python main.py")
    print("أو:")
    print("  python run.py")
    
    print("\nبيانات تسجيل الدخول:")
    print("  اسم المستخدم: admin")
    print("  كلمة المرور: (التي أدخلتها)")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف الإعداد")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ فادح في الإعداد: {e}")
        sys.exit(1)
