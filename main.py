#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الأكاديمية التعليمية
Academy Management System

الملف الرئيسي لتشغيل التطبيق
Main Application Entry Point
"""

import sys
import os
import logging
from datetime import datetime
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# إعداد PyQt6
try:
    from PyQt6.QtWidgets import QApplication, QMessageBox
    from PyQt6.QtCore import Qt, QTranslator, QLocale
    from PyQt6.QtGui import QIcon, QFont
except ImportError:
    print("خطأ: PyQt6 غير مثبت. يرجى تثبيته باستخدام: pip install PyQt6")
    sys.exit(1)

# استيراد مكونات التطبيق
try:
    from database.db_handler import db_handler
    from views.login_screen import LoginScreen
    from views.main_window import MainWindow
    from utils.logger import setup_logging
    from utils.validators import ConfigValidator
except ImportError as e:
    print(f"خطأ في استيراد المكونات: {e}")
    sys.exit(1)

class AcademyApp:
    """التطبيق الرئيسي لنظام إدارة الأكاديمية"""
    
    def __init__(self):
        """تهيئة التطبيق"""
        self.app = None
        self.main_window = None
        self.login_screen = None
        self.current_user = None
        self.logger = None
        
        # إعداد المجلدات المطلوبة
        self.setup_directories()
        
        # إعداد نظام السجلات
        self.setup_logging()
        
        # إعداد قاعدة البيانات
        self.setup_database()
    
    def setup_directories(self):
        """إنشاء المجلدات المطلوبة"""
        directories = [
            'logs',
            'exports', 
            'database/backup',
            'resources/images',
            'resources/themes',
            'resources/localization'
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def setup_logging(self):
        """إعداد نظام السجلات"""
        try:
            setup_logging()
            self.logger = logging.getLogger(__name__)
            self.logger.info("تم بدء تشغيل نظام إدارة الأكاديمية التعليمية")
        except Exception as e:
            print(f"خطأ في إعداد نظام السجلات: {e}")
    
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            # تهيئة قاعدة البيانات
            db_handler.initialize_database()
            self.logger.info("تم إعداد قاعدة البيانات بنجاح")
        except Exception as e:
            self.logger.error(f"خطأ في إعداد قاعدة البيانات: {e}")
            self.show_error("خطأ في قاعدة البيانات", 
                          f"فشل في إعداد قاعدة البيانات:\n{e}")
            sys.exit(1)
    
    def setup_application(self):
        """إعداد تطبيق PyQt6"""
        try:
            # إنشاء تطبيق PyQt6
            self.app = QApplication(sys.argv)
            
            # إعداد خصائص التطبيق
            self.app.setApplicationName("نظام إدارة الأكاديمية التعليمية")
            self.app.setApplicationVersion("1.0.0")
            self.app.setOrganizationName("الأكاديمية التعليمية")
            
            # إعداد الخط الافتراضي للعربية
            font = QFont("Arial", 10)
            font.setStyleHint(QFont.StyleHint.SansSerif)
            self.app.setFont(font)
            
            # إعداد الأيقونة
            icon_path = "resources/images/logo.png"
            if os.path.exists(icon_path):
                self.app.setWindowIcon(QIcon(icon_path))
            
            # إعداد اتجاه النص للعربية
            self.app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
            
            self.logger.info("تم إعداد تطبيق PyQt6 بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في إعداد التطبيق: {e}")
            self.show_error("خطأ في التطبيق", f"فشل في إعداد التطبيق:\n{e}")
            sys.exit(1)
    
    def show_login(self):
        """عرض شاشة تسجيل الدخول"""
        try:
            self.login_screen = LoginScreen()
            self.login_screen.login_successful.connect(self.on_login_successful)
            self.login_screen.show()
            
            self.logger.info("تم عرض شاشة تسجيل الدخول")
            
        except Exception as e:
            self.logger.error(f"خطأ في عرض شاشة تسجيل الدخول: {e}")
            self.show_error("خطأ", f"فشل في عرض شاشة تسجيل الدخول:\n{e}")
    
    def on_login_successful(self, user):
        """معالج نجاح تسجيل الدخول"""
        try:
            self.current_user = user
            self.logger.info(f"تم تسجيل دخول المستخدم: {user.username}")
            
            # إخفاء شاشة تسجيل الدخول
            if self.login_screen:
                self.login_screen.hide()
            
            # عرض النافذة الرئيسية
            self.show_main_window()
            
        except Exception as e:
            self.logger.error(f"خطأ في معالجة تسجيل الدخول: {e}")
            self.show_error("خطأ", f"فشل في تسجيل الدخول:\n{e}")
    
    def show_main_window(self):
        """عرض النافذة الرئيسية"""
        try:
            self.main_window = MainWindow(self.current_user)
            self.main_window.logout_requested.connect(self.on_logout)
            self.main_window.show()
            
            self.logger.info("تم عرض النافذة الرئيسية")
            
        except Exception as e:
            self.logger.error(f"خطأ في عرض النافذة الرئيسية: {e}")
            self.show_error("خطأ", f"فشل في عرض النافذة الرئيسية:\n{e}")
    
    def on_logout(self):
        """معالج تسجيل الخروج"""
        try:
            if self.current_user:
                self.logger.info(f"تم تسجيل خروج المستخدم: {self.current_user.username}")
                self.current_user = None
            
            # إخفاء النافذة الرئيسية
            if self.main_window:
                self.main_window.hide()
                self.main_window = None
            
            # عرض شاشة تسجيل الدخول مرة أخرى
            self.show_login()
            
        except Exception as e:
            self.logger.error(f"خطأ في تسجيل الخروج: {e}")
            self.show_error("خطأ", f"فشل في تسجيل الخروج:\n{e}")
    
    def show_error(self, title: str, message: str):
        """عرض رسالة خطأ"""
        if hasattr(self, 'app') and self.app:
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Icon.Critical)
            msg_box.setWindowTitle(title)
            msg_box.setText(message)
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg_box.exec()
        else:
            print(f"{title}: {message}")
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            # إعداد التطبيق
            self.setup_application()
            
            # عرض شاشة تسجيل الدخول
            self.show_login()
            
            # تشغيل حلقة الأحداث
            exit_code = self.app.exec()
            
            # تنظيف الموارد
            self.cleanup()
            
            return exit_code
            
        except KeyboardInterrupt:
            self.logger.info("تم إيقاف التطبيق بواسطة المستخدم")
            return 0
        except Exception as e:
            self.logger.error(f"خطأ في تشغيل التطبيق: {e}")
            self.show_error("خطأ فادح", f"فشل في تشغيل التطبيق:\n{e}")
            return 1
    
    def cleanup(self):
        """تنظيف الموارد"""
        try:
            # إغلاق اتصال قاعدة البيانات
            if db_handler:
                db_handler.close()
            
            self.logger.info("تم إغلاق التطبيق بنجاح")
            
        except Exception as e:
            print(f"خطأ في تنظيف الموارد: {e}")

def main():
    """الدالة الرئيسية"""
    try:
        # إنشاء وتشغيل التطبيق
        app = AcademyApp()
        return app.run()
        
    except Exception as e:
        print(f"خطأ فادح في التطبيق: {e}")
        return 1

if __name__ == "__main__":
    # تشغيل التطبيق
    exit_code = main()
    sys.exit(exit_code)
