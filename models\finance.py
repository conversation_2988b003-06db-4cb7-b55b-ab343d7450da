"""
نماذج المالية - Finance Models
"""

import logging
from datetime import datetime, date
from typing import Optional, Dict, Any, List
from decimal import Decimal
from database.db_handler import db_handler

class Payment:
    """نموذج المدفوعات"""
    
    # أنواع المدفوعات
    PAYMENT_TYPES = {
        'tuition': 'رسوم دراسية',
        'registration': 'رسوم تسجيل',
        'materials': 'رسوم مواد',
        'other': 'أخرى'
    }
    
    # طرق الدفع
    PAYMENT_METHODS = {
        'cash': 'نقداً',
        'card': 'بطاقة',
        'bank_transfer': 'تحويل بنكي',
        'check': 'شيك'
    }
    
    # حالات الدفع
    STATUSES = {
        'pending': 'معلق',
        'paid': 'مدفوع',
        'overdue': 'متأخر',
        'cancelled': 'ملغي'
    }
    
    def __init__(self, payment_id: int = None, student_id: int = None,
                 group_id: int = None, amount: Decimal = None,
                 payment_type: str = None, payment_method: str = None,
                 payment_date: date = None, due_date: date = None,
                 status: str = 'pending', receipt_number: str = None,
                 notes: str = None, recorded_by: int = None,
                 created_at: datetime = None):
        """
        تهيئة نموذج المدفوعات
        """
        self.id = payment_id
        self.student_id = student_id
        self.group_id = group_id
        self.amount = amount
        self.payment_type = payment_type
        self.payment_method = payment_method
        self.payment_date = payment_date
        self.due_date = due_date
        self.status = status
        self.receipt_number = receipt_number
        self.notes = notes
        self.recorded_by = recorded_by
        self.created_at = created_at or datetime.now()
        self.logger = logging.getLogger(__name__)
    
    @classmethod
    def create(cls, student_id: int, amount: Decimal, payment_type: str,
               **kwargs) -> 'Payment':
        """
        إنشاء دفعة جديدة
        
        Args:
            student_id: معرف الطالب
            amount: المبلغ
            payment_type: نوع الدفعة
            **kwargs: باقي البيانات
            
        Returns:
            Payment: الدفعة الجديدة
        """
        # التحقق من صحة البيانات
        if not student_id or not amount or not payment_type:
            raise ValueError("معرف الطالب والمبلغ ونوع الدفعة مطلوبة")
        
        if payment_type not in cls.PAYMENT_TYPES:
            raise ValueError(f"نوع الدفعة غير صحيح. الأنواع المتاحة: {list(cls.PAYMENT_TYPES.keys())}")
        
        if amount <= 0:
            raise ValueError("المبلغ يجب أن يكون أكبر من صفر")
        
        # إعداد البيانات
        payment_data = {
            'student_id': student_id,
            'amount': amount,
            'payment_type': payment_type,
            'status': 'pending',
            'created_at': datetime.now()
        }
        
        # إضافة البيانات الإضافية
        allowed_fields = [
            'group_id', 'payment_method', 'payment_date', 'due_date',
            'receipt_number', 'notes', 'recorded_by'
        ]
        
        for field in allowed_fields:
            if field in kwargs and kwargs[field] is not None:
                payment_data[field] = kwargs[field]
        
        # إنشاء رقم الإيصال إذا لم يتم تمريره
        if 'receipt_number' not in payment_data or not payment_data['receipt_number']:
            payment_data['receipt_number'] = cls._generate_receipt_number()
        
        # إدراج في قاعدة البيانات
        payment_id = db_handler.insert('payments', payment_data)
        
        # إنشاء كائن الدفعة
        payment = cls(
            payment_id=payment_id,
            student_id=student_id,
            amount=amount,
            payment_type=payment_type,
            **kwargs
        )
        
        payment.logger.info(f"تم إنشاء دفعة جديدة: {amount} للطالب {student_id}")
        return payment
    
    @classmethod
    def get_by_id(cls, payment_id: int) -> Optional['Payment']:
        """الحصول على دفعة بالمعرف"""
        query = "SELECT * FROM payments WHERE id = ?"
        row = db_handler.fetch_one(query, (payment_id,))
        
        if row:
            return cls._from_row(row)
        return None
    
    @classmethod
    def get_by_receipt_number(cls, receipt_number: str) -> Optional['Payment']:
        """الحصول على دفعة برقم الإيصال"""
        query = "SELECT * FROM payments WHERE receipt_number = ?"
        row = db_handler.fetch_one(query, (receipt_number,))
        
        if row:
            return cls._from_row(row)
        return None
    
    @classmethod
    def get_by_student(cls, student_id: int, status: str = None) -> List['Payment']:
        """الحصول على دفعات الطالب"""
        query = "SELECT * FROM payments WHERE student_id = ?"
        params = [student_id]
        
        if status:
            query += " AND status = ?"
            params.append(status)
        
        query += " ORDER BY created_at DESC"
        
        rows = db_handler.fetch_all(query, tuple(params))
        return [cls._from_row(row) for row in rows]
    
    @classmethod
    def search(cls, search_term: str = None, student_id: int = None,
               payment_type: str = None, status: str = None,
               start_date: date = None, end_date: date = None,
               limit: int = None) -> List['Payment']:
        """
        البحث عن الدفعات
        """
        query = "SELECT * FROM payments WHERE 1=1"
        params = []
        
        if search_term:
            query += " AND receipt_number LIKE ?"
            params.append(f"%{search_term}%")
        
        if student_id:
            query += " AND student_id = ?"
            params.append(student_id)
        
        if payment_type:
            query += " AND payment_type = ?"
            params.append(payment_type)
        
        if status:
            query += " AND status = ?"
            params.append(status)
        
        if start_date:
            query += " AND payment_date >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND payment_date <= ?"
            params.append(end_date)
        
        query += " ORDER BY created_at DESC"
        
        if limit:
            query += f" LIMIT {limit}"
        
        rows = db_handler.fetch_all(query, tuple(params) if params else None)
        return [cls._from_row(row) for row in rows]
    
    @classmethod
    def _generate_receipt_number(cls) -> str:
        """إنشاء رقم إيصال جديد"""
        # الحصول على آخر رقم إيصال
        query = "SELECT receipt_number FROM payments ORDER BY id DESC LIMIT 1"
        row = db_handler.fetch_one(query)
        
        if row and row['receipt_number']:
            # استخراج الرقم من آخر إيصال
            last_receipt = row['receipt_number']
            if last_receipt.startswith('RCP'):
                try:
                    last_number = int(last_receipt[3:])
                    new_number = last_number + 1
                except ValueError:
                    new_number = 1
            else:
                new_number = 1
        else:
            new_number = 1
        
        return f"RCP{new_number:06d}"  # RCP000001, RCP000002, etc.
    
    def update(self, **kwargs) -> bool:
        """تحديث بيانات الدفعة"""
        try:
            # الحقول المسموح بتحديثها
            allowed_fields = [
                'amount', 'payment_method', 'payment_date', 'due_date',
                'status', 'notes'
            ]
            
            data = {}
            for key, value in kwargs.items():
                if key in allowed_fields:
                    data[key] = value
                    setattr(self, key, value)
            
            if data:
                rows_affected = db_handler.update('payments', data, 'id = ?', (self.id,))
                
                if rows_affected > 0:
                    self.logger.info(f"تم تحديث بيانات الدفعة: {self.receipt_number}")
                    return True
            
            return False
        except Exception as e:
            self.logger.error(f"خطأ في تحديث بيانات الدفعة: {e}")
            return False
    
    def mark_as_paid(self, payment_date: date = None, payment_method: str = None) -> bool:
        """تحديد الدفعة كمدفوعة"""
        return self.update(
            status='paid',
            payment_date=payment_date or date.today(),
            payment_method=payment_method
        )
    
    def cancel(self, notes: str = None) -> bool:
        """إلغاء الدفعة"""
        return self.update(status='cancelled', notes=notes)
    
    @classmethod
    def _from_row(cls, row) -> 'Payment':
        """إنشاء كائن دفعة من سجل قاعدة البيانات"""
        return cls(
            payment_id=row['id'],
            student_id=row['student_id'],
            group_id=row['group_id'],
            amount=Decimal(str(row['amount'])) if row['amount'] else None,
            payment_type=row['payment_type'],
            payment_method=row['payment_method'],
            payment_date=date.fromisoformat(row['payment_date']) if row['payment_date'] else None,
            due_date=date.fromisoformat(row['due_date']) if row['due_date'] else None,
            status=row['status'],
            receipt_number=row['receipt_number'],
            notes=row['notes'],
            recorded_by=row['recorded_by'],
            created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل الدفعة إلى قاموس"""
        return {
            'id': self.id,
            'student_id': self.student_id,
            'group_id': self.group_id,
            'amount': float(self.amount) if self.amount else None,
            'payment_type': self.payment_type,
            'payment_type_name': self.PAYMENT_TYPES.get(self.payment_type, self.payment_type),
            'payment_method': self.payment_method,
            'payment_method_name': self.PAYMENT_METHODS.get(self.payment_method, self.payment_method) if self.payment_method else None,
            'payment_date': self.payment_date.isoformat() if self.payment_date else None,
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'status': self.status,
            'status_name': self.STATUSES.get(self.status, self.status),
            'receipt_number': self.receipt_number,
            'notes': self.notes,
            'recorded_by': self.recorded_by,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }


class TeacherSalary:
    """نموذج رواتب المعلمين"""
    
    # حالات الراتب
    STATUSES = {
        'pending': 'معلق',
        'paid': 'مدفوع',
        'cancelled': 'ملغي'
    }
    
    def __init__(self, salary_id: int = None, teacher_id: int = None,
                 month: int = None, year: int = None,
                 base_salary: Decimal = None, bonus: Decimal = None,
                 deductions: Decimal = None, total_amount: Decimal = None,
                 payment_date: date = None, status: str = 'pending',
                 notes: str = None, recorded_by: int = None,
                 created_at: datetime = None):
        """
        تهيئة نموذج راتب المعلم
        """
        self.id = salary_id
        self.teacher_id = teacher_id
        self.month = month
        self.year = year
        self.base_salary = base_salary
        self.bonus = bonus or Decimal('0')
        self.deductions = deductions or Decimal('0')
        self.total_amount = total_amount
        self.payment_date = payment_date
        self.status = status
        self.notes = notes
        self.recorded_by = recorded_by
        self.created_at = created_at or datetime.now()
        self.logger = logging.getLogger(__name__)
    
    @property
    def calculated_total(self) -> Decimal:
        """حساب المجموع الإجمالي"""
        if self.base_salary:
            return self.base_salary + self.bonus - self.deductions
        return Decimal('0')
    
    @classmethod
    def create(cls, teacher_id: int, month: int, year: int,
               base_salary: Decimal, **kwargs) -> 'TeacherSalary':
        """
        إنشاء راتب معلم جديد
        """
        # التحقق من صحة البيانات
        if not teacher_id or not month or not year or not base_salary:
            raise ValueError("معرف المعلم والشهر والسنة والراتب الأساسي مطلوبة")
        
        if not (1 <= month <= 12):
            raise ValueError("الشهر يجب أن يكون بين 1 و 12")
        
        if base_salary <= 0:
            raise ValueError("الراتب الأساسي يجب أن يكون أكبر من صفر")
        
        # التحقق من عدم وجود راتب لنفس الشهر والسنة
        existing = cls.get_by_teacher_month_year(teacher_id, month, year)
        if existing:
            raise ValueError("راتب هذا الشهر موجود مسبقاً")
        
        # حساب المجموع الإجمالي
        bonus = kwargs.get('bonus', Decimal('0'))
        deductions = kwargs.get('deductions', Decimal('0'))
        total_amount = base_salary + bonus - deductions
        
        # إعداد البيانات
        salary_data = {
            'teacher_id': teacher_id,
            'month': month,
            'year': year,
            'base_salary': base_salary,
            'bonus': bonus,
            'deductions': deductions,
            'total_amount': total_amount,
            'status': 'pending',
            'created_at': datetime.now()
        }
        
        # إضافة البيانات الإضافية
        allowed_fields = ['payment_date', 'notes', 'recorded_by']
        
        for field in allowed_fields:
            if field in kwargs and kwargs[field] is not None:
                salary_data[field] = kwargs[field]
        
        # إدراج في قاعدة البيانات
        salary_id = db_handler.insert('teacher_salaries', salary_data)
        
        # إنشاء كائن الراتب
        salary = cls(
            salary_id=salary_id,
            teacher_id=teacher_id,
            month=month,
            year=year,
            base_salary=base_salary,
            bonus=bonus,
            deductions=deductions,
            total_amount=total_amount,
            **kwargs
        )
        
        salary.logger.info(f"تم إنشاء راتب جديد: معلم {teacher_id} - {month}/{year}")
        return salary
    
    @classmethod
    def get_by_id(cls, salary_id: int) -> Optional['TeacherSalary']:
        """الحصول على راتب بالمعرف"""
        query = "SELECT * FROM teacher_salaries WHERE id = ?"
        row = db_handler.fetch_one(query, (salary_id,))
        
        if row:
            return cls._from_row(row)
        return None
    
    @classmethod
    def get_by_teacher_month_year(cls, teacher_id: int, month: int, year: int) -> Optional['TeacherSalary']:
        """الحصول على راتب معلم لشهر وسنة محددة"""
        query = "SELECT * FROM teacher_salaries WHERE teacher_id = ? AND month = ? AND year = ?"
        row = db_handler.fetch_one(query, (teacher_id, month, year))
        
        if row:
            return cls._from_row(row)
        return None
    
    @classmethod
    def get_by_teacher(cls, teacher_id: int, year: int = None) -> List['TeacherSalary']:
        """الحصول على رواتب المعلم"""
        query = "SELECT * FROM teacher_salaries WHERE teacher_id = ?"
        params = [teacher_id]
        
        if year:
            query += " AND year = ?"
            params.append(year)
        
        query += " ORDER BY year DESC, month DESC"
        
        rows = db_handler.fetch_all(query, tuple(params))
        return [cls._from_row(row) for row in rows]
    
    def update(self, **kwargs) -> bool:
        """تحديث بيانات الراتب"""
        try:
            # الحقول المسموح بتحديثها
            allowed_fields = [
                'base_salary', 'bonus', 'deductions', 'payment_date',
                'status', 'notes'
            ]
            
            data = {}
            for key, value in kwargs.items():
                if key in allowed_fields:
                    data[key] = value
                    setattr(self, key, value)
            
            # إعادة حساب المجموع الإجمالي إذا تم تحديث المبالغ
            if any(field in data for field in ['base_salary', 'bonus', 'deductions']):
                data['total_amount'] = self.calculated_total
                self.total_amount = data['total_amount']
            
            if data:
                rows_affected = db_handler.update('teacher_salaries', data, 'id = ?', (self.id,))
                
                if rows_affected > 0:
                    self.logger.info(f"تم تحديث بيانات الراتب: معلم {self.teacher_id} - {self.month}/{self.year}")
                    return True
            
            return False
        except Exception as e:
            self.logger.error(f"خطأ في تحديث بيانات الراتب: {e}")
            return False
    
    def mark_as_paid(self, payment_date: date = None) -> bool:
        """تحديد الراتب كمدفوع"""
        return self.update(
            status='paid',
            payment_date=payment_date or date.today()
        )
    
    @classmethod
    def _from_row(cls, row) -> 'TeacherSalary':
        """إنشاء كائن راتب من سجل قاعدة البيانات"""
        return cls(
            salary_id=row['id'],
            teacher_id=row['teacher_id'],
            month=row['month'],
            year=row['year'],
            base_salary=Decimal(str(row['base_salary'])) if row['base_salary'] else None,
            bonus=Decimal(str(row['bonus'])) if row['bonus'] else Decimal('0'),
            deductions=Decimal(str(row['deductions'])) if row['deductions'] else Decimal('0'),
            total_amount=Decimal(str(row['total_amount'])) if row['total_amount'] else None,
            payment_date=date.fromisoformat(row['payment_date']) if row['payment_date'] else None,
            status=row['status'],
            notes=row['notes'],
            recorded_by=row['recorded_by'],
            created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل الراتب إلى قاموس"""
        return {
            'id': self.id,
            'teacher_id': self.teacher_id,
            'month': self.month,
            'year': self.year,
            'base_salary': float(self.base_salary) if self.base_salary else None,
            'bonus': float(self.bonus),
            'deductions': float(self.deductions),
            'total_amount': float(self.total_amount) if self.total_amount else None,
            'payment_date': self.payment_date.isoformat() if self.payment_date else None,
            'status': self.status,
            'status_name': self.STATUSES.get(self.status, self.status),
            'notes': self.notes,
            'recorded_by': self.recorded_by,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
