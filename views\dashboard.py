"""
لوحة التحكم الرئيسية
Main Dashboard Widget
"""

import sys
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QScrollArea,
    QGroupBox, QProgressBar, QTableWidget, QTableWidgetItem,
    QHeaderView, QSizePolicy
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap, QPalette, QColor, QIcon

from models.student import Student
from models.teacher import Teacher
from models.course import Course
from models.group import Group
from models.finance import Payment
from models.user import User


class StatCard(QFrame):
    """بطاقة إحصائية"""
    
    clicked = pyqtSignal()
    
    def __init__(self, title, value, icon=None, color="#3498db"):
        super().__init__()
        self.setFrameStyle(QFrame.Shape.Box)
        self.setLineWidth(1)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {color};
                font-size: 14px;
                font-weight: bold;
                margin: 5px;
            }}
        """)
        layout.addWidget(title_label)
        
        # القيمة
        value_label = QLabel(str(value))
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        value_label.setStyleSheet(f"""
            QLabel {{
                color: {color};
                font-size: 32px;
                font-weight: bold;
                margin: 10px;
            }}
        """)
        layout.addWidget(value_label)
        
        # تنسيق البطاقة
        self.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 10px;
                margin: 5px;
            }}
            QFrame:hover {{
                background-color: #f8f9fa;
                border-width: 3px;
            }}
        """)
        
        self.setMinimumHeight(120)
        self.setMaximumHeight(150)
    
    def mousePressEvent(self, event):
        """عند النقر على البطاقة"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)


class QuickActionButton(QPushButton):
    """زر إجراء سريع"""
    
    def __init__(self, text, icon=None, color="#2ecc71"):
        super().__init__(text)
        self.setMinimumHeight(50)
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px;
                margin: 5px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.3)};
            }}
        """)
    
    def darken_color(self, color, factor=0.2):
        """تغميق اللون"""
        # تحويل بسيط للون
        if color == "#2ecc71":
            return "#27ae60" if factor == 0.2 else "#1e8449"
        elif color == "#e74c3c":
            return "#c0392b" if factor == 0.2 else "#a93226"
        elif color == "#f39c12":
            return "#e67e22" if factor == 0.2 else "#d35400"
        elif color == "#9b59b6":
            return "#8e44ad" if factor == 0.2 else "#7d3c98"
        else:
            return color


class RecentActivityWidget(QWidget):
    """ويدجت النشاطات الأخيرة"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.load_recent_activities()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # عنوان القسم
        title_label = QLabel("النشاطات الأخيرة")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # جدول النشاطات
        self.activities_table = QTableWidget()
        self.activities_table.setColumnCount(3)
        self.activities_table.setHorizontalHeaderLabels(["الوقت", "المستخدم", "النشاط"])
        
        # تخصيص الجدول
        header = self.activities_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        
        self.activities_table.setAlternatingRowColors(True)
        self.activities_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.activities_table.setMaximumHeight(200)
        
        self.activities_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #ecf0f1;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        
        layout.addWidget(self.activities_table)
    
    def load_recent_activities(self):
        """تحميل النشاطات الأخيرة"""
        try:
            # قراءة آخر 10 نشاطات من ملف السجل
            import os
            log_file = "logs/activity.log"
            
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # أخذ آخر 10 أسطر
                recent_lines = lines[-10:] if len(lines) >= 10 else lines
                recent_lines.reverse()  # عكس الترتيب لإظهار الأحدث أولاً
                
                self.activities_table.setRowCount(len(recent_lines))
                
                for row, line in enumerate(recent_lines):
                    try:
                        # تحليل السطر: التاريخ - المستخدم - النشاط
                        parts = line.strip().split(' - ')
                        if len(parts) >= 3:
                            time_part = parts[0]
                            user_part = parts[1].split('|')[0].replace('المستخدم: ', '')
                            activity_part = parts[2] if len(parts) > 2 else parts[1].split('|')[1].replace(' النشاط: ', '') if '|' in parts[1] else ''
                            
                            self.activities_table.setItem(row, 0, QTableWidgetItem(time_part))
                            self.activities_table.setItem(row, 1, QTableWidgetItem(user_part))
                            self.activities_table.setItem(row, 2, QTableWidgetItem(activity_part))
                    except:
                        continue
            else:
                self.activities_table.setRowCount(1)
                self.activities_table.setItem(0, 0, QTableWidgetItem("--"))
                self.activities_table.setItem(0, 1, QTableWidgetItem("--"))
                self.activities_table.setItem(0, 2, QTableWidgetItem("لا توجد نشاطات مسجلة"))
                
        except Exception as e:
            self.activities_table.setRowCount(1)
            self.activities_table.setItem(0, 0, QTableWidgetItem("خطأ"))
            self.activities_table.setItem(0, 1, QTableWidgetItem("--"))
            self.activities_table.setItem(0, 2, QTableWidgetItem(f"خطأ في تحميل النشاطات: {str(e)}"))


class DashboardWidget(QWidget):
    """لوحة التحكم الرئيسية"""
    
    # إشارات للتنقل
    navigate_to_students = pyqtSignal()
    navigate_to_teachers = pyqtSignal()
    navigate_to_courses = pyqtSignal()
    navigate_to_groups = pyqtSignal()
    navigate_to_payments = pyqtSignal()
    navigate_to_attendance = pyqtSignal()
    
    def __init__(self, current_user=None):
        super().__init__()
        self.current_user = current_user
        self.setup_ui()
        self.load_statistics()
        
        # تحديث الإحصائيات كل دقيقة
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_statistics)
        self.refresh_timer.start(60000)  # 60 ثانية
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        
        # ترحيب
        welcome_layout = QHBoxLayout()
        
        welcome_label = QLabel(f"مرحباً، {self.current_user.full_name if self.current_user else 'المستخدم'}")
        welcome_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
            }
        """)
        welcome_layout.addWidget(welcome_label)
        
        welcome_layout.addStretch()
        
        # تاريخ اليوم
        from datetime import datetime
        today_label = QLabel(datetime.now().strftime("%A، %d %B %Y"))
        today_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #7f8c8d;
                padding: 15px;
            }
        """)
        welcome_layout.addWidget(today_label)
        
        main_layout.addLayout(welcome_layout)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # بطاقات الإحصائيات
        stats_group = QGroupBox("الإحصائيات العامة")
        stats_layout = QGridLayout(stats_group)
        
        # إنشاء البطاقات
        self.students_card = StatCard("عدد الطلاب", "0", color="#3498db")
        self.students_card.clicked.connect(self.navigate_to_students.emit)
        stats_layout.addWidget(self.students_card, 0, 0)
        
        self.teachers_card = StatCard("عدد المعلمين", "0", color="#e74c3c")
        self.teachers_card.clicked.connect(self.navigate_to_teachers.emit)
        stats_layout.addWidget(self.teachers_card, 0, 1)
        
        self.courses_card = StatCard("عدد الكورسات", "0", color="#f39c12")
        self.courses_card.clicked.connect(self.navigate_to_courses.emit)
        stats_layout.addWidget(self.courses_card, 0, 2)
        
        self.groups_card = StatCard("المجموعات النشطة", "0", color="#9b59b6")
        self.groups_card.clicked.connect(self.navigate_to_groups.emit)
        stats_layout.addWidget(self.groups_card, 0, 3)
        
        self.payments_card = StatCard("المدفوعات اليوم", "0", color="#2ecc71")
        self.payments_card.clicked.connect(self.navigate_to_payments.emit)
        stats_layout.addWidget(self.payments_card, 1, 0)
        
        self.attendance_card = StatCard("الحضور اليوم", "0", color="#1abc9c")
        self.attendance_card.clicked.connect(self.navigate_to_attendance.emit)
        stats_layout.addWidget(self.attendance_card, 1, 1)
        
        scroll_layout.addWidget(stats_group)
        
        # الإجراءات السريعة
        actions_group = QGroupBox("الإجراءات السريعة")
        actions_layout = QGridLayout(actions_group)
        
        add_student_btn = QuickActionButton("إضافة طالب جديد", color="#2ecc71")
        add_student_btn.clicked.connect(self.navigate_to_students.emit)
        actions_layout.addWidget(add_student_btn, 0, 0)
        
        add_teacher_btn = QuickActionButton("إضافة معلم جديد", color="#e74c3c")
        add_teacher_btn.clicked.connect(self.navigate_to_teachers.emit)
        actions_layout.addWidget(add_teacher_btn, 0, 1)
        
        add_course_btn = QuickActionButton("إضافة كورس جديد", color="#f39c12")
        add_course_btn.clicked.connect(self.navigate_to_courses.emit)
        actions_layout.addWidget(add_course_btn, 0, 2)
        
        record_payment_btn = QuickActionButton("تسجيل دفعة", color="#9b59b6")
        record_payment_btn.clicked.connect(self.navigate_to_payments.emit)
        actions_layout.addWidget(record_payment_btn, 1, 0)
        
        record_attendance_btn = QuickActionButton("تسجيل حضور", color="#1abc9c")
        record_attendance_btn.clicked.connect(self.navigate_to_attendance.emit)
        actions_layout.addWidget(record_attendance_btn, 1, 1)
        
        scroll_layout.addWidget(actions_group)
        
        # النشاطات الأخيرة
        self.recent_activities = RecentActivityWidget()
        scroll_layout.addWidget(self.recent_activities)
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        main_layout.addWidget(scroll_area)
        
        # تنسيق عام
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #2c3e50;
                font-size: 16px;
            }
        """)
    
    def load_statistics(self):
        """تحميل الإحصائيات"""
        try:
            # إحصائيات الطلاب
            students_count = self.get_students_count()
            self.update_card_value(self.students_card, students_count)
            
            # إحصائيات المعلمين
            teachers_count = self.get_teachers_count()
            self.update_card_value(self.teachers_card, teachers_count)
            
            # إحصائيات الكورسات
            courses_count = self.get_courses_count()
            self.update_card_value(self.courses_card, courses_count)
            
            # إحصائيات المجموعات النشطة
            active_groups_count = self.get_active_groups_count()
            self.update_card_value(self.groups_card, active_groups_count)
            
            # إحصائيات المدفوعات اليوم
            today_payments = self.get_today_payments_count()
            self.update_card_value(self.payments_card, today_payments)
            
            # إحصائيات الحضور اليوم
            today_attendance = self.get_today_attendance_count()
            self.update_card_value(self.attendance_card, today_attendance)
            
            # تحديث النشاطات الأخيرة
            self.recent_activities.load_recent_activities()
            
        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات: {e}")
    
    def update_card_value(self, card, value):
        """تحديث قيمة البطاقة"""
        # البحث عن QLabel الثاني (القيمة) في البطاقة
        for child in card.children():
            if isinstance(child, QVBoxLayout):
                for i in range(child.count()):
                    widget = child.itemAt(i).widget()
                    if isinstance(widget, QLabel) and widget.styleSheet().find("font-size: 32px") != -1:
                        widget.setText(str(value))
                        break
    
    def get_students_count(self):
        """الحصول على عدد الطلاب"""
        try:
            students = Student.get_all()
            return len(students)
        except:
            return 0
    
    def get_teachers_count(self):
        """الحصول على عدد المعلمين"""
        try:
            teachers = Teacher.get_all()
            return len(teachers)
        except:
            return 0
    
    def get_courses_count(self):
        """الحصول على عدد الكورسات"""
        try:
            courses = Course.get_all()
            return len(courses)
        except:
            return 0
    
    def get_active_groups_count(self):
        """الحصول على عدد المجموعات النشطة"""
        try:
            groups = Group.get_all()
            active_groups = [g for g in groups if g.status == 'active']
            return len(active_groups)
        except:
            return 0
    
    def get_today_payments_count(self):
        """الحصول على عدد المدفوعات اليوم"""
        try:
            from datetime import date
            today = date.today()
            payments = Payment.search(start_date=today, end_date=today)
            return len(payments)
        except:
            return 0
    
    def get_today_attendance_count(self):
        """الحصول على عدد سجلات الحضور اليوم"""
        try:
            from datetime import date
            from models.attendance import Attendance
            today = date.today()
            # هذا يتطلب تنفيذ دالة في نموذج الحضور
            # attendance_records = Attendance.get_by_date(today)
            # return len(attendance_records)
            return 0  # مؤقتاً
        except:
            return 0
