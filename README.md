# نظام إدارة الأكاديمية التعليمية
## Academy Management System

نظام متكامل لإدارة الأكاديميات التعليمية باستخدام Python و PyQt6

---

## 📋 المحتويات

- [نظرة عامة](#نظرة-عامة)
- [الميزات](#الميزات)
- [متطلبات النظام](#متطلبات-النظام)
- [التثبيت](#التثبيت)
- [الاستخدام](#الاستخدام)
- [هيكل المشروع](#هيكل-المشروع)
- [قاعدة البيانات](#قاعدة-البيانات)
- [الأمان](#الأمان)
- [المساهمة](#المساهمة)
- [الترخيص](#الترخيص)

---

## 🎯 نظرة عامة

نظام إدارة الأكاديمية التعليمية هو تطبيق سطح مكتب متكامل مصمم لإدارة جميع جوانب الأكاديميات التعليمية. يوفر النظام واجهة سهلة الاستخدام لإدارة الطلاب والمعلمين والكورسات والمجموعات والحضور والمالية.

### 🎨 لقطات الشاشة

*سيتم إضافة لقطات الشاشة لاحقاً*

---

## ✨ الميزات

### 👥 إدارة المستخدمين
- نظام تسجيل دخول آمن
- أدوار متعددة (مدير، معلم، موظف، طالب)
- صلاحيات مخصصة لكل دور
- تشفير كلمات المرور

### 🎓 إدارة الطلاب
- تسجيل الطلاب الجدد
- إدارة البيانات الشخصية
- تتبع الحالة الأكاديمية
- ربط الطلاب بالمجموعات

### 👨‍🏫 إدارة المعلمين
- إدارة بيانات المعلمين
- تعيين المعلمين للكورسات
- إدارة الرواتب والمكافآت
- تتبع عبء العمل

### 📚 إدارة الكورسات
- إنشاء وتعديل الكورسات
- تصنيف الكورسات حسب المستوى
- تحديد الأسعار والمدة
- إحصائيات الكورسات

### 👥 إدارة المجموعات
- إنشاء مجموعات للكورسات
- جدولة الحصص
- إدارة الطاقة الاستيعابية
- ربط المعلمين بالمجموعات

### 📊 الحضور والغياب
- تسجيل الحضور اليومي
- تقارير الحضور التفصيلية
- إشعارات الغياب
- إحصائيات الحضور

### 💰 النظام المالي
- إدارة مدفوعات الطلاب
- تتبع الرسوم والأقساط
- إدارة رواتب المعلمين
- تقارير مالية شاملة

### 📈 التقارير والإحصائيات
- تقارير الطلاب
- تقارير الحضور
- التقارير المالية
- تصدير بصيغ متعددة (PDF, Excel, CSV)

### 🔧 الإعدادات والأدوات
- نسخ احتياطية تلقائية
- تصدير واستيراد البيانات
- سجل النشاطات
- إعدادات النظام

---

## 💻 متطلبات النظام

### الحد الأدنى
- **نظام التشغيل:** Windows 10, macOS 10.14, Ubuntu 18.04
- **Python:** 3.8 أو أحدث
- **الذاكرة:** 4 GB RAM
- **التخزين:** 500 MB مساحة فارغة

### المستحسن
- **نظام التشغيل:** Windows 11, macOS 12, Ubuntu 20.04
- **Python:** 3.10 أو أحدث
- **الذاكرة:** 8 GB RAM
- **التخزين:** 2 GB مساحة فارغة

---

## 🚀 التثبيت

### 1. تحميل المشروع
```bash
git clone https://github.com/your-repo/academy-management-system.git
cd academy-management-system
```

### 2. إنشاء بيئة افتراضية
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد قاعدة البيانات
```bash
python -c "from database.db_handler import db_handler; db_handler.initialize_database()"
```

### 5. تشغيل التطبيق
```bash
python main.py
```

---

## 📖 الاستخدام

### تسجيل الدخول الأولي
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

⚠️ **تحذير:** يرجى تغيير كلمة المرور الافتراضية فور تسجيل الدخول الأول.

### الواجهة الرئيسية
1. **الشريط الجانبي:** يحتوي على قائمة التنقل الرئيسية
2. **منطقة المحتوى:** تعرض الواجهات المختلفة
3. **شريط الحالة:** يعرض معلومات المستخدم والوقت

### إدارة البيانات
- استخدم القوائم الجانبية للتنقل بين الأقسام
- اتبع التعليمات المعروضة في كل واجهة
- احفظ البيانات بانتظام

---

## 📁 هيكل المشروع

```
academy_management_system/
├── main.py                     # نقطة تشغيل البرنامج
├── config.ini                  # إعدادات النظام
├── requirements.txt            # المتطلبات
├── README.md                   # هذا الملف
│
├── database/                   # قاعدة البيانات
│   ├── db_handler.py          # معالج قاعدة البيانات
│   ├── schema.sql             # مخطط قاعدة البيانات
│   └── backup/                # النسخ الاحتياطية
│
├── models/                     # نماذج البيانات
│   ├── __init__.py
│   ├── user.py                # نموذج المستخدم
│   ├── student.py             # نموذج الطالب
│   ├── teacher.py             # نموذج المعلم
│   ├── course.py              # نموذج الكورس
│   ├── group.py               # نموذج المجموعة
│   ├── attendance.py          # نموذج الحضور
│   └── finance.py             # النماذج المالية
│
├── views/                      # واجهات المستخدم
│   ├── __init__.py
│   ├── login_screen.py        # شاشة تسجيل الدخول
│   └── main_window.py         # النافذة الرئيسية
│
├── controllers/                # طبقة التحكم
│   └── (قيد التطوير)
│
├── utils/                      # أدوات مساعدة
│   ├── __init__.py
│   ├── logger.py              # نظام السجلات
│   ├── validators.py          # مدققات البيانات
│   ├── export.py              # أدوات التصدير
│   └── backup.py              # أدوات النسخ الاحتياطي
│
├── resources/                  # الموارد
│   ├── images/                # الصور
│   ├── themes/                # السمات
│   └── localization/          # الترجمات
│
├── tests/                      # الاختبارات
│   └── (قيد التطوير)
│
├── logs/                       # ملفات السجلات
├── exports/                    # ملفات التصدير
└── database/backup/           # النسخ الاحتياطية
```

---

## 🗄️ قاعدة البيانات

### الجداول الرئيسية
- **users:** المستخدمون والصلاحيات
- **students:** بيانات الطلاب
- **teachers:** بيانات المعلمين
- **courses:** الكورسات المتاحة
- **groups:** مجموعات الدراسة
- **attendance:** سجلات الحضور
- **payments:** المدفوعات
- **teacher_salaries:** رواتب المعلمين
- **activity_logs:** سجل النشاطات

### النسخ الاحتياطية
- نسخ احتياطية تلقائية يومية
- إمكانية إنشاء نسخ يدوية
- استعادة سهلة من النسخ الاحتياطية

---

## 🔒 الأمان

### تشفير البيانات
- كلمات المرور مشفرة باستخدام bcrypt
- حماية من SQL Injection
- تشفير البيانات الحساسة

### إدارة الجلسات
- انتهاء صلاحية الجلسات تلقائياً
- تسجيل جميع النشاطات
- حماية من المحاولات المتكررة

### النسخ الاحتياطية
- نسخ احتياطية مشفرة
- تخزين آمن للبيانات
- استعادة سريعة عند الحاجة

---

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

### إرشادات المساهمة
- اتبع معايير الكود المستخدمة
- أضف اختبارات للميزات الجديدة
- حدث التوثيق عند الحاجة
- تأكد من عمل جميع الاختبارات

---

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

## 📞 الدعم والتواصل

- **البريد الإلكتروني:** <EMAIL>
- **الموقع:** https://academy-system.com
- **التوثيق:** https://docs.academy-system.com

---

## 🙏 شكر وتقدير

- فريق تطوير PyQt6
- مجتمع Python
- جميع المساهمين في المشروع

---

**© 2024 نظام إدارة الأكاديمية التعليمية. جميع الحقوق محفوظة.**
