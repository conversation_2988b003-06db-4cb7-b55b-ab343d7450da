#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام إدارة الأكاديمية التعليمية
Academy Management System Launcher
"""

import sys
import os
from pathlib import Path

def main():
    """تشغيل النظام"""
    print("🎓 نظام إدارة الأكاديمية التعليمية")
    print("Academy Management System")
    print("=" * 40)
    
    # إضافة مسار المشروع
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    try:
        # تهيئة قاعدة البيانات إذا لم تكن موجودة
        db_path = "database/academy_db.sqlite"
        if not os.path.exists(db_path):
            print("🔧 إعداد قاعدة البيانات للمرة الأولى...")
            from database.db_handler import db_handler
            db_handler.initialize_database()
            print("✅ تم إعداد قاعدة البيانات")
        
        # تشغيل التطبيق الرئيسي
        print("🚀 تشغيل النظام...")
        from main import main as run_main
        return run_main()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print("يرجى تثبيت المتطلبات: pip install -r requirements.txt")
        return 1
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف النظام")
        sys.exit(0)
