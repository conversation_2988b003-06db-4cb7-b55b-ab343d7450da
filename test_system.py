#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام - System Test
اختبار بسيط للتأكد من عمل المكونات الأساسية
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """اختبار استيراد المكونات الأساسية"""
    print("🔍 اختبار استيراد المكونات...")
    
    try:
        # اختبار استيراد قاعدة البيانات
        from database.db_handler import db_handler
        print("✅ تم استيراد معالج قاعدة البيانات بنجاح")
        
        # اختبار استيراد النماذج
        from models.user import User
        from models.student import Student
        from models.teacher import Teacher
        from models.course import Course
        from models.group import Group
        from models.attendance import Attendance
        from models.finance import Payment, TeacherSalary
        print("✅ تم استيراد جميع النماذج بنجاح")
        
        # اختبار استيراد الأدوات
        from utils.logger import setup_logging
        from utils.validators import DataValidator
        from utils.export import ExportManager
        from utils.backup import BackupManager
        print("✅ تم استيراد الأدوات المساعدة بنجاح")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🗄️ اختبار قاعدة البيانات...")
    
    try:
        from database.db_handler import db_handler
        
        # تهيئة قاعدة البيانات
        db_handler.initialize_database()
        print("✅ تم تهيئة قاعدة البيانات بنجاح")
        
        # اختبار الاتصال
        connection = db_handler.get_connection()
        if connection:
            print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # اختبار الجداول
        tables = db_handler.get_all_tables()
        expected_tables = [
            'users', 'students', 'teachers', 'courses', 'groups',
            'student_groups', 'attendance', 'payments', 'teacher_salaries',
            'activity_logs', 'settings'
        ]
        
        missing_tables = [table for table in expected_tables if table not in tables]
        if missing_tables:
            print(f"⚠️ جداول مفقودة: {missing_tables}")
        else:
            print("✅ جميع الجداول موجودة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_user_model():
    """اختبار نموذج المستخدم"""
    print("\n👤 اختبار نموذج المستخدم...")
    
    try:
        from models.user import User
        
        # اختبار المستخدم الافتراضي
        admin_user = User.get_by_username('admin')
        if admin_user:
            print("✅ المستخدم الافتراضي موجود")
            
            # اختبار تسجيل الدخول
            auth_user = User.authenticate('admin', 'admin123')
            if auth_user:
                print("✅ تسجيل الدخول يعمل بشكل صحيح")
            else:
                print("⚠️ فشل في تسجيل الدخول - قد تحتاج لتحديث كلمة المرور")
        else:
            print("⚠️ المستخدم الافتراضي غير موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نموذج المستخدم: {e}")
        return False

def test_validators():
    """اختبار مدققات البيانات"""
    print("\n✔️ اختبار مدققات البيانات...")
    
    try:
        from utils.validators import DataValidator
        
        # اختبار البريد الإلكتروني
        valid_email = DataValidator.validate_email("<EMAIL>")
        invalid_email = DataValidator.validate_email("invalid-email")
        
        if valid_email and not invalid_email:
            print("✅ مدقق البريد الإلكتروني يعمل بشكل صحيح")
        else:
            print("❌ مشكلة في مدقق البريد الإلكتروني")
        
        # اختبار رقم الهاتف
        valid_phone = DataValidator.validate_phone("0501234567")
        invalid_phone = DataValidator.validate_phone("123")
        
        if valid_phone and not invalid_phone:
            print("✅ مدقق رقم الهاتف يعمل بشكل صحيح")
        else:
            print("❌ مشكلة في مدقق رقم الهاتف")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في المدققات: {e}")
        return False

def test_logging():
    """اختبار نظام السجلات"""
    print("\n📝 اختبار نظام السجلات...")
    
    try:
        from utils.logger import setup_logging, activity_logger
        import logging
        
        # إعداد نظام السجلات
        setup_logging()
        print("✅ تم إعداد نظام السجلات بنجاح")
        
        # اختبار كتابة سجل
        logger = logging.getLogger("test")
        logger.info("رسالة اختبار")
        print("✅ تم كتابة رسالة اختبار في السجل")
        
        # اختبار مسجل النشاطات
        activity_logger.log_user_action(1, "test_user", "اختبار النظام")
        print("✅ تم تسجيل نشاط اختبار")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نظام السجلات: {e}")
        return False

def test_backup():
    """اختبار النسخ الاحتياطية"""
    print("\n💾 اختبار النسخ الاحتياطية...")
    
    try:
        from utils.backup import BackupManager
        
        backup_manager = BackupManager()
        
        # اختبار إنشاء نسخة احتياطية
        backup_path = backup_manager.create_database_backup("test_backup")
        if os.path.exists(backup_path):
            print("✅ تم إنشاء نسخة احتياطية بنجاح")
            
            # حذف النسخة الاختبارية
            os.remove(backup_path)
            print("✅ تم حذف النسخة الاختبارية")
        else:
            print("❌ فشل في إنشاء النسخة الاحتياطية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في النسخ الاحتياطية: {e}")
        return False

def test_pyqt():
    """اختبار PyQt6"""
    print("\n🖥️ اختبار PyQt6...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        
        # إنشاء تطبيق اختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("✅ PyQt6 متاح ويعمل بشكل صحيح")
        
        # تنظيف
        if app:
            app.quit()
        
        return True
        
    except ImportError:
        print("❌ PyQt6 غير مثبت أو لا يعمل بشكل صحيح")
        return False
    except Exception as e:
        print(f"❌ خطأ في PyQt6: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار نظام إدارة الأكاديمية التعليمية")
    print("=" * 50)
    
    tests = [
        ("استيراد المكونات", test_imports),
        ("قاعدة البيانات", test_database),
        ("نموذج المستخدم", test_user_model),
        ("مدققات البيانات", test_validators),
        ("نظام السجلات", test_logging),
        ("النسخ الاحتياطية", test_backup),
        ("PyQt6", test_pyqt),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
        return 0
    else:
        print(f"\n⚠️ فشل {failed} اختبار. يرجى مراجعة الأخطاء أعلاه.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
