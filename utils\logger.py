"""
نظام السجلات - Logging System
"""

import logging
import logging.handlers
import os
from datetime import datetime
from pathlib import Path

def setup_logging(log_level: str = 'INFO', log_dir: str = 'logs'):
    """
    إعداد نظام السجلات
    
    Args:
        log_level: مستوى السجل (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_dir: مجلد السجلات
    """
    # إنشاء مجلد السجلات
    Path(log_dir).mkdir(exist_ok=True)
    
    # تحديد مستوى السجل
    level = getattr(logging, log_level.upper(), logging.INFO)
    
    # إعداد التنسيق
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # إعداد السجل الجذر
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # إزالة المعالجات الموجودة
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # معالج ملف السجل الرئيسي
    main_log_file = os.path.join(log_dir, 'academy.log')
    file_handler = logging.handlers.RotatingFileHandler(
        main_log_file,
        maxBytes=10*1024*1024,  # 10 MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(level)
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)
    
    # معالج ملف الأخطاء
    error_log_file = os.path.join(log_dir, 'errors.log')
    error_handler = logging.handlers.RotatingFileHandler(
        error_log_file,
        maxBytes=5*1024*1024,  # 5 MB
        backupCount=3,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(formatter)
    root_logger.addHandler(error_handler)
    
    # معالج وحدة التحكم (للتطوير)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.WARNING)
    console_formatter = logging.Formatter(
        '%(levelname)s - %(name)s - %(message)s'
    )
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # سجل بداية التشغيل
    logger = logging.getLogger(__name__)
    logger.info("تم إعداد نظام السجلات بنجاح")
    logger.info(f"مستوى السجل: {log_level}")
    logger.info(f"مجلد السجلات: {log_dir}")

class ActivityLogger:
    """مسجل النشاطات"""
    
    def __init__(self):
        self.logger = logging.getLogger('activity')
        
        # إعداد معالج منفصل للنشاطات
        activity_log_file = 'logs/activity.log'
        handler = logging.handlers.RotatingFileHandler(
            activity_log_file,
            maxBytes=20*1024*1024,  # 20 MB
            backupCount=10,
            encoding='utf-8'
        )
        
        formatter = logging.Formatter(
            '%(asctime)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
        self.logger.propagate = False
    
    def log_user_action(self, user_id: int, username: str, action: str, 
                       details: str = None, ip_address: str = None):
        """
        تسجيل نشاط المستخدم
        
        Args:
            user_id: معرف المستخدم
            username: اسم المستخدم
            action: النشاط
            details: تفاصيل إضافية
            ip_address: عنوان IP
        """
        message_parts = [
            f"المستخدم: {username} (ID: {user_id})",
            f"النشاط: {action}"
        ]
        
        if details:
            message_parts.append(f"التفاصيل: {details}")
        
        if ip_address:
            message_parts.append(f"IP: {ip_address}")
        
        message = " | ".join(message_parts)
        self.logger.info(message)
    
    def log_login(self, user_id: int, username: str, success: bool, 
                  ip_address: str = None):
        """تسجيل محاولة تسجيل الدخول"""
        status = "نجح" if success else "فشل"
        action = f"تسجيل دخول - {status}"
        self.log_user_action(user_id, username, action, ip_address=ip_address)
    
    def log_logout(self, user_id: int, username: str):
        """تسجيل تسجيل الخروج"""
        self.log_user_action(user_id, username, "تسجيل خروج")
    
    def log_data_change(self, user_id: int, username: str, table: str, 
                       record_id: int, action: str, changes: dict = None):
        """
        تسجيل تغيير البيانات
        
        Args:
            user_id: معرف المستخدم
            username: اسم المستخدم
            table: اسم الجدول
            record_id: معرف السجل
            action: نوع العملية (إضافة، تعديل، حذف)
            changes: التغييرات المطبقة
        """
        details = f"الجدول: {table}, السجل: {record_id}"
        
        if changes:
            changes_str = ", ".join([f"{k}: {v}" for k, v in changes.items()])
            details += f", التغييرات: {changes_str}"
        
        self.log_user_action(user_id, username, f"تغيير بيانات - {action}", details)

# إنشاء مثيل عام لمسجل النشاطات
activity_logger = ActivityLogger()
