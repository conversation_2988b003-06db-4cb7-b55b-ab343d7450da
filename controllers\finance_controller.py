"""
تحكم المالية - Finance Controller
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import date, datetime
from decimal import Decimal

from models.finance import Payment, TeacherSalary
from models.student import Student
from models.teacher import Teacher
from models.user import User
from utils.validators import DataValidator
from utils.logger import activity_logger

class FinanceController:
    """تحكم العمليات المالية"""
    
    def __init__(self, current_user: User):
        """
        تهيئة تحكم المالية
        
        Args:
            current_user: المستخدم الحالي
        """
        self.current_user = current_user
        self.logger = logging.getLogger(__name__)
    
    def create_payment(self, payment_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        إنشاء دفعة جديدة
        
        Args:
            payment_data: بيانات الدفعة
            
        Returns:
            Dict: نتيجة العملية
        """
        try:
            # التحقق من الصلاحيات
            if not self.current_user.has_permission('manage_payments'):
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لإدارة المدفوعات'
                }
            
            # التحقق من صحة البيانات
            required_fields = ['student_id', 'amount', 'payment_type']
            for field in required_fields:
                if not payment_data.get(field):
                    return {
                        'success': False,
                        'message': f"الحقل {field} مطلوب"
                    }
            
            # التحقق من وجود الطالب
            student = Student.get_by_id(payment_data['student_id'])
            if not student:
                return {
                    'success': False,
                    'message': 'الطالب غير موجود'
                }
            
            # التحقق من صحة المبلغ
            if not DataValidator.validate_amount(payment_data['amount']):
                return {
                    'success': False,
                    'message': 'المبلغ غير صحيح'
                }
            
            # تحويل المبلغ إلى Decimal
            payment_data['amount'] = Decimal(str(payment_data['amount']))
            
            # التحقق من نوع الدفعة
            if payment_data['payment_type'] not in Payment.PAYMENT_TYPES:
                return {
                    'success': False,
                    'message': f"نوع الدفعة غير صحيح. الأنواع المتاحة: {list(Payment.PAYMENT_TYPES.keys())}"
                }
            
            # التحقق من طريقة الدفع
            if payment_data.get('payment_method') and payment_data['payment_method'] not in Payment.PAYMENT_METHODS:
                return {
                    'success': False,
                    'message': f"طريقة الدفع غير صحيحة. الطرق المتاحة: {list(Payment.PAYMENT_METHODS.keys())}"
                }
            
            # إضافة معرف المسجل
            payment_data['recorded_by'] = self.current_user.id
            
            # إنشاء الدفعة
            payment = Payment.create(**payment_data)
            
            # تسجيل النشاط
            activity_logger.log_user_action(
                self.current_user.id,
                self.current_user.username,
                'إضافة دفعة',
                f"تم إضافة دفعة للطالب {student.full_name} - {payment.amount} ريال"
            )
            
            return {
                'success': True,
                'message': 'تم إنشاء الدفعة بنجاح',
                'payment': payment.to_dict()
            }
            
        except ValueError as e:
            return {
                'success': False,
                'message': str(e)
            }
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الدفعة: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def get_payment(self, payment_id: int) -> Dict[str, Any]:
        """
        الحصول على بيانات دفعة
        
        Args:
            payment_id: معرف الدفعة
            
        Returns:
            Dict: بيانات الدفعة
        """
        try:
            # التحقق من الصلاحيات
            if not self.current_user.has_permission('view_payments'):
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لعرض المدفوعات'
                }
            
            payment = Payment.get_by_id(payment_id)
            if not payment:
                return {
                    'success': False,
                    'message': 'الدفعة غير موجودة'
                }
            
            return {
                'success': True,
                'payment': payment.to_dict()
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الدفعة: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def update_payment(self, payment_id: int, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        تحديث بيانات دفعة
        
        Args:
            payment_id: معرف الدفعة
            update_data: البيانات المحدثة
            
        Returns:
            Dict: نتيجة العملية
        """
        try:
            # التحقق من الصلاحيات
            if not self.current_user.has_permission('manage_payments'):
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لتعديل المدفوعات'
                }
            
            payment = Payment.get_by_id(payment_id)
            if not payment:
                return {
                    'success': False,
                    'message': 'الدفعة غير موجودة'
                }
            
            # تحويل المبلغ إلى Decimal إذا كان موجوداً
            if 'amount' in update_data:
                if not DataValidator.validate_amount(update_data['amount']):
                    return {
                        'success': False,
                        'message': 'المبلغ غير صحيح'
                    }
                update_data['amount'] = Decimal(str(update_data['amount']))
            
            # التحقق من طريقة الدفع
            if update_data.get('payment_method') and update_data['payment_method'] not in Payment.PAYMENT_METHODS:
                return {
                    'success': False,
                    'message': f"طريقة الدفع غير صحيحة. الطرق المتاحة: {list(Payment.PAYMENT_METHODS.keys())}"
                }
            
            # التحقق من الحالة
            if update_data.get('status') and update_data['status'] not in Payment.STATUSES:
                return {
                    'success': False,
                    'message': f"حالة الدفعة غير صحيحة. الحالات المتاحة: {list(Payment.STATUSES.keys())}"
                }
            
            if payment.update(**update_data):
                # تسجيل النشاط
                activity_logger.log_user_action(
                    self.current_user.id,
                    self.current_user.username,
                    'تعديل دفعة',
                    f"تم تعديل الدفعة {payment.receipt_number}"
                )
                
                return {
                    'success': True,
                    'message': 'تم تحديث بيانات الدفعة بنجاح',
                    'payment': payment.to_dict()
                }
            else:
                return {
                    'success': False,
                    'message': 'فشل في تحديث البيانات'
                }
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديث الدفعة: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def mark_payment_as_paid(self, payment_id: int, payment_date: date = None,
                            payment_method: str = None) -> Dict[str, Any]:
        """
        تحديد الدفعة كمدفوعة
        
        Args:
            payment_id: معرف الدفعة
            payment_date: تاريخ الدفع
            payment_method: طريقة الدفع
            
        Returns:
            Dict: نتيجة العملية
        """
        try:
            payment = Payment.get_by_id(payment_id)
            if not payment:
                return {
                    'success': False,
                    'message': 'الدفعة غير موجودة'
                }
            
            if payment.mark_as_paid(payment_date, payment_method):
                # تسجيل النشاط
                activity_logger.log_user_action(
                    self.current_user.id,
                    self.current_user.username,
                    'تأكيد دفعة',
                    f"تم تأكيد دفع الدفعة {payment.receipt_number}"
                )
                
                return {
                    'success': True,
                    'message': 'تم تأكيد الدفعة بنجاح',
                    'payment': payment.to_dict()
                }
            else:
                return {
                    'success': False,
                    'message': 'فشل في تأكيد الدفعة'
                }
            
        except Exception as e:
            self.logger.error(f"خطأ في تأكيد الدفعة: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def search_payments(self, search_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        البحث عن المدفوعات
        
        Args:
            search_params: معاملات البحث
            
        Returns:
            Dict: نتائج البحث
        """
        try:
            # التحقق من الصلاحيات
            if not self.current_user.has_permission('view_payments'):
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لعرض المدفوعات'
                }
            
            payments = Payment.search(
                search_term=search_params.get('search_term'),
                student_id=search_params.get('student_id'),
                payment_type=search_params.get('payment_type'),
                status=search_params.get('status'),
                start_date=search_params.get('start_date'),
                end_date=search_params.get('end_date'),
                limit=search_params.get('limit', 50)
            )
            
            return {
                'success': True,
                'payments': [payment.to_dict() for payment in payments],
                'count': len(payments)
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في البحث عن المدفوعات: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def get_student_payments(self, student_id: int, status: str = None) -> Dict[str, Any]:
        """
        الحصول على مدفوعات طالب
        
        Args:
            student_id: معرف الطالب
            status: حالة المدفوعات
            
        Returns:
            Dict: مدفوعات الطالب
        """
        try:
            student = Student.get_by_id(student_id)
            if not student:
                return {
                    'success': False,
                    'message': 'الطالب غير موجود'
                }
            
            payments = Payment.get_by_student(student_id, status)
            
            return {
                'success': True,
                'payments': [payment.to_dict() for payment in payments],
                'student_info': student.to_dict()
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على مدفوعات الطالب: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    # رواتب المعلمين
    
    def create_teacher_salary(self, salary_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        إنشاء راتب معلم
        
        Args:
            salary_data: بيانات الراتب
            
        Returns:
            Dict: نتيجة العملية
        """
        try:
            # التحقق من الصلاحيات (المدير فقط)
            if self.current_user.role != 'admin':
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لإدارة رواتب المعلمين'
                }
            
            # التحقق من صحة البيانات
            required_fields = ['teacher_id', 'month', 'year', 'base_salary']
            for field in required_fields:
                if not salary_data.get(field):
                    return {
                        'success': False,
                        'message': f"الحقل {field} مطلوب"
                    }
            
            # التحقق من وجود المعلم
            teacher = Teacher.get_by_id(salary_data['teacher_id'])
            if not teacher:
                return {
                    'success': False,
                    'message': 'المعلم غير موجود'
                }
            
            # تحويل المبالغ إلى Decimal
            for field in ['base_salary', 'bonus', 'deductions']:
                if field in salary_data and salary_data[field]:
                    salary_data[field] = Decimal(str(salary_data[field]))
            
            # إضافة معرف المسجل
            salary_data['recorded_by'] = self.current_user.id
            
            # إنشاء الراتب
            salary = TeacherSalary.create(**salary_data)
            
            # تسجيل النشاط
            activity_logger.log_user_action(
                self.current_user.id,
                self.current_user.username,
                'إضافة راتب معلم',
                f"تم إضافة راتب للمعلم {teacher.full_name} - {salary.month}/{salary.year}"
            )
            
            return {
                'success': True,
                'message': 'تم إنشاء راتب المعلم بنجاح',
                'salary': salary.to_dict()
            }
            
        except ValueError as e:
            return {
                'success': False,
                'message': str(e)
            }
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء راتب المعلم: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def get_teacher_salaries(self, teacher_id: int, year: int = None) -> Dict[str, Any]:
        """
        الحصول على رواتب معلم
        
        Args:
            teacher_id: معرف المعلم
            year: السنة (اختياري)
            
        Returns:
            Dict: رواتب المعلم
        """
        try:
            # التحقق من الصلاحيات (المدير فقط)
            if self.current_user.role != 'admin':
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لعرض رواتب المعلمين'
                }
            
            teacher = Teacher.get_by_id(teacher_id)
            if not teacher:
                return {
                    'success': False,
                    'message': 'المعلم غير موجود'
                }
            
            salaries = TeacherSalary.get_by_teacher(teacher_id, year)
            
            return {
                'success': True,
                'salaries': [salary.to_dict() for salary in salaries],
                'teacher_info': teacher.to_dict()
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على رواتب المعلم: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def mark_salary_as_paid(self, salary_id: int, payment_date: date = None) -> Dict[str, Any]:
        """
        تحديد راتب كمدفوع
        
        Args:
            salary_id: معرف الراتب
            payment_date: تاريخ الدفع
            
        Returns:
            Dict: نتيجة العملية
        """
        try:
            # التحقق من الصلاحيات (المدير فقط)
            if self.current_user.role != 'admin':
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لإدارة رواتب المعلمين'
                }
            
            salary = TeacherSalary.get_by_id(salary_id)
            if not salary:
                return {
                    'success': False,
                    'message': 'الراتب غير موجود'
                }
            
            if salary.mark_as_paid(payment_date):
                # تسجيل النشاط
                activity_logger.log_user_action(
                    self.current_user.id,
                    self.current_user.username,
                    'تأكيد دفع راتب',
                    f"تم تأكيد دفع راتب المعلم - {salary.month}/{salary.year}"
                )
                
                return {
                    'success': True,
                    'message': 'تم تأكيد دفع الراتب بنجاح',
                    'salary': salary.to_dict()
                }
            else:
                return {
                    'success': False,
                    'message': 'فشل في تأكيد دفع الراتب'
                }
            
        except Exception as e:
            self.logger.error(f"خطأ في تأكيد دفع الراتب: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
