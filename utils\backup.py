"""
أدوات النسخ الاحتياطي - Backup Utilities
"""

import os
import shutil
import zipfile
import logging
import schedule
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Optional, Dict, Any

from database.db_handler import db_handler

class BackupManager:
    """مدير النسخ الاحتياطية"""
    
    def __init__(self, backup_dir: str = "database/backup"):
        """
        تهيئة مدير النسخ الاحتياطية
        
        Args:
            backup_dir: مجلد النسخ الاحتياطية
        """
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        self.logger = logging.getLogger(__name__)
        self.scheduler_running = False
        self.scheduler_thread = None
    
    def create_database_backup(self, backup_name: str = None) -> str:
        """
        إنشاء نسخة احتياطية من قاعدة البيانات
        
        Args:
            backup_name: اسم النسخة الاحتياطية
            
        Returns:
            str: مسار النسخة الاحتياطية
        """
        try:
            if not backup_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"academy_backup_{timestamp}.sqlite"
            
            if not backup_name.endswith('.sqlite'):
                backup_name += '.sqlite'
            
            backup_path = self.backup_dir / backup_name
            
            # إنشاء النسخة الاحتياطية
            backup_file = db_handler.backup_database(str(backup_path))
            
            self.logger.info(f"تم إنشاء نسخة احتياطية: {backup_file}")
            return backup_file
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            raise
    
    def create_full_backup(self, backup_name: str = None) -> str:
        """
        إنشاء نسخة احتياطية كاملة (قاعدة البيانات + الملفات)
        
        Args:
            backup_name: اسم النسخة الاحتياطية
            
        Returns:
            str: مسار النسخة الاحتياطية المضغوطة
        """
        try:
            if not backup_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"academy_full_backup_{timestamp}"
            
            backup_zip_path = self.backup_dir / f"{backup_name}.zip"
            
            with zipfile.ZipFile(backup_zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # إضافة قاعدة البيانات
                db_path = "database/academy_db.sqlite"
                if os.path.exists(db_path):
                    zipf.write(db_path, "academy_db.sqlite")
                
                # إضافة ملفات الإعدادات
                config_path = "config.ini"
                if os.path.exists(config_path):
                    zipf.write(config_path, "config.ini")
                
                # إضافة السجلات (آخر 7 أيام فقط)
                logs_dir = Path("logs")
                if logs_dir.exists():
                    cutoff_date = datetime.now() - timedelta(days=7)
                    
                    for log_file in logs_dir.glob("*.log"):
                        if log_file.stat().st_mtime > cutoff_date.timestamp():
                            zipf.write(log_file, f"logs/{log_file.name}")
                
                # إضافة الموارد
                resources_dir = Path("resources")
                if resources_dir.exists():
                    for resource_file in resources_dir.rglob("*"):
                        if resource_file.is_file():
                            arcname = str(resource_file.relative_to("."))
                            zipf.write(resource_file, arcname)
            
            self.logger.info(f"تم إنشاء نسخة احتياطية كاملة: {backup_zip_path}")
            return str(backup_zip_path)
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء النسخة الاحتياطية الكاملة: {e}")
            raise
    
    def restore_database_backup(self, backup_path: str) -> bool:
        """
        استعادة نسخة احتياطية من قاعدة البيانات
        
        Args:
            backup_path: مسار النسخة الاحتياطية
            
        Returns:
            bool: نجح الاستعادة أم لا
        """
        try:
            if not os.path.exists(backup_path):
                raise FileNotFoundError(f"النسخة الاحتياطية غير موجودة: {backup_path}")
            
            # إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
            current_backup = self.create_database_backup("before_restore_backup")
            
            try:
                # استعادة النسخة الاحتياطية
                db_handler.restore_database(backup_path)
                
                self.logger.info(f"تم استعادة قاعدة البيانات من: {backup_path}")
                return True
                
            except Exception as e:
                # في حالة فشل الاستعادة، استعادة النسخة الأصلية
                self.logger.error(f"فشل في استعادة النسخة الاحتياطية، استعادة النسخة الأصلية: {e}")
                db_handler.restore_database(current_backup)
                raise
                
        except Exception as e:
            self.logger.error(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            return False
    
    def restore_full_backup(self, backup_zip_path: str) -> bool:
        """
        استعادة نسخة احتياطية كاملة
        
        Args:
            backup_zip_path: مسار النسخة الاحتياطية المضغوطة
            
        Returns:
            bool: نجح الاستعادة أم لا
        """
        try:
            if not os.path.exists(backup_zip_path):
                raise FileNotFoundError(f"النسخة الاحتياطية غير موجودة: {backup_zip_path}")
            
            # إنشاء نسخة احتياطية من الحالة الحالية
            current_backup = self.create_full_backup("before_restore_backup")
            
            try:
                with zipfile.ZipFile(backup_zip_path, 'r') as zipf:
                    # استخراج الملفات
                    zipf.extractall(".")
                
                self.logger.info(f"تم استعادة النسخة الاحتياطية الكاملة من: {backup_zip_path}")
                return True
                
            except Exception as e:
                self.logger.error(f"فشل في استعادة النسخة الاحتياطية الكاملة: {e}")
                raise
                
        except Exception as e:
            self.logger.error(f"خطأ في استعادة النسخة الاحتياطية الكاملة: {e}")
            return False
    
    def list_backups(self) -> List[Dict[str, Any]]:
        """
        الحصول على قائمة النسخ الاحتياطية
        
        Returns:
            List[Dict]: قائمة النسخ الاحتياطية مع معلوماتها
        """
        backups = []
        
        try:
            for backup_file in self.backup_dir.glob("*"):
                if backup_file.is_file():
                    stat = backup_file.stat()
                    
                    backup_info = {
                        'name': backup_file.name,
                        'path': str(backup_file),
                        'size': stat.st_size,
                        'size_mb': round(stat.st_size / (1024 * 1024), 2),
                        'created_at': datetime.fromtimestamp(stat.st_ctime),
                        'modified_at': datetime.fromtimestamp(stat.st_mtime),
                        'type': 'full' if backup_file.suffix == '.zip' else 'database'
                    }
                    
                    backups.append(backup_info)
            
            # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
            backups.sort(key=lambda x: x['created_at'], reverse=True)
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على قائمة النسخ الاحتياطية: {e}")
        
        return backups
    
    def delete_backup(self, backup_path: str) -> bool:
        """
        حذف نسخة احتياطية
        
        Args:
            backup_path: مسار النسخة الاحتياطية
            
        Returns:
            bool: نجح الحذف أم لا
        """
        try:
            if os.path.exists(backup_path):
                os.remove(backup_path)
                self.logger.info(f"تم حذف النسخة الاحتياطية: {backup_path}")
                return True
            else:
                self.logger.warning(f"النسخة الاحتياطية غير موجودة: {backup_path}")
                return False
                
        except Exception as e:
            self.logger.error(f"خطأ في حذف النسخة الاحتياطية: {e}")
            return False
    
    def cleanup_old_backups(self, keep_days: int = 30, keep_count: int = 10) -> int:
        """
        تنظيف النسخ الاحتياطية القديمة
        
        Args:
            keep_days: عدد الأيام للاحتفاظ بالنسخ
            keep_count: عدد النسخ للاحتفاظ بها على الأقل
            
        Returns:
            int: عدد النسخ المحذوفة
        """
        deleted_count = 0
        
        try:
            backups = self.list_backups()
            cutoff_date = datetime.now() - timedelta(days=keep_days)
            
            # الاحتفاظ بعدد معين من النسخ الحديثة
            backups_to_keep = backups[:keep_count]
            backups_to_check = backups[keep_count:]
            
            for backup in backups_to_check:
                if backup['created_at'] < cutoff_date:
                    if self.delete_backup(backup['path']):
                        deleted_count += 1
            
            self.logger.info(f"تم حذف {deleted_count} نسخة احتياطية قديمة")
            
        except Exception as e:
            self.logger.error(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {e}")
        
        return deleted_count
    
    def schedule_automatic_backup(self, interval_hours: int = 24, 
                                 backup_type: str = 'database') -> bool:
        """
        جدولة النسخ الاحتياطية التلقائية
        
        Args:
            interval_hours: فترة النسخ الاحتياطي بالساعات
            backup_type: نوع النسخ الاحتياطي ('database' أو 'full')
            
        Returns:
            bool: نجح الجدولة أم لا
        """
        try:
            # إيقاف الجدولة السابقة إن وجدت
            self.stop_automatic_backup()
            
            def backup_job():
                try:
                    if backup_type == 'full':
                        self.create_full_backup()
                    else:
                        self.create_database_backup()
                    
                    # تنظيف النسخ القديمة
                    self.cleanup_old_backups()
                    
                except Exception as e:
                    self.logger.error(f"خطأ في النسخ الاحتياطي التلقائي: {e}")
            
            # جدولة المهمة
            schedule.every(interval_hours).hours.do(backup_job)
            
            # تشغيل الجدولة في خيط منفصل
            def run_scheduler():
                self.scheduler_running = True
                while self.scheduler_running:
                    schedule.run_pending()
                    time.sleep(60)  # فحص كل دقيقة
            
            self.scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
            self.scheduler_thread.start()
            
            self.logger.info(f"تم جدولة النسخ الاحتياطي التلقائي كل {interval_hours} ساعة")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في جدولة النسخ الاحتياطي التلقائي: {e}")
            return False
    
    def stop_automatic_backup(self):
        """إيقاف النسخ الاحتياطي التلقائي"""
        try:
            self.scheduler_running = False
            schedule.clear()
            
            if self.scheduler_thread and self.scheduler_thread.is_alive():
                self.scheduler_thread.join(timeout=5)
            
            self.logger.info("تم إيقاف النسخ الاحتياطي التلقائي")
            
        except Exception as e:
            self.logger.error(f"خطأ في إيقاف النسخ الاحتياطي التلقائي: {e}")
    
    def get_backup_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات النسخ الاحتياطية"""
        try:
            backups = self.list_backups()
            
            total_count = len(backups)
            total_size = sum(backup['size'] for backup in backups)
            
            database_backups = [b for b in backups if b['type'] == 'database']
            full_backups = [b for b in backups if b['type'] == 'full']
            
            latest_backup = backups[0] if backups else None
            
            return {
                'total_count': total_count,
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'database_backups_count': len(database_backups),
                'full_backups_count': len(full_backups),
                'latest_backup': latest_backup,
                'scheduler_running': self.scheduler_running
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات النسخ الاحتياطية: {e}")
            return {}

# إنشاء مثيل عام لمدير النسخ الاحتياطية
backup_manager = BackupManager()
