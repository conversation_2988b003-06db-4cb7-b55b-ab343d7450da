"""
نموذج الحضور والغياب - Attendance Model
"""

import logging
from datetime import datetime, date
from typing import Optional, Dict, Any, List
from database.db_handler import db_handler

class Attendance:
    """نموذج الحضور والغياب"""
    
    # حالات الحضور المتاحة
    STATUSES = {
        'present': 'حاضر',
        'absent': 'غائب',
        'late': 'متأخر',
        'excused': 'غياب بعذر'
    }
    
    def __init__(self, attendance_id: int = None, student_id: int = None,
                 group_id: int = None, date: date = None, status: str = None,
                 notes: str = None, recorded_by: int = None,
                 recorded_at: datetime = None):
        """
        تهيئة نموذج الحضور والغياب
        """
        self.id = attendance_id
        self.student_id = student_id
        self.group_id = group_id
        self.date = date
        self.status = status
        self.notes = notes
        self.recorded_by = recorded_by
        self.recorded_at = recorded_at or datetime.now()
        self.logger = logging.getLogger(__name__)
    
    @classmethod
    def record(cls, student_id: int, group_id: int, status: str,
               attendance_date: date = None, notes: str = None,
               recorded_by: int = None) -> 'Attendance':
        """
        تسجيل حضور/غياب
        
        Args:
            student_id: معرف الطالب
            group_id: معرف المجموعة
            status: حالة الحضور
            attendance_date: تاريخ الحضور
            notes: ملاحظات
            recorded_by: معرف المسجل
            
        Returns:
            Attendance: سجل الحضور
        """
        # التحقق من صحة البيانات
        if not student_id or not group_id or not status:
            raise ValueError("معرف الطالب ومعرف المجموعة وحالة الحضور مطلوبة")
        
        if status not in cls.STATUSES:
            raise ValueError(f"حالة الحضور غير صحيحة. الحالات المتاحة: {list(cls.STATUSES.keys())}")
        
        if not attendance_date:
            attendance_date = date.today()
        
        # التحقق من وجود سجل سابق لنفس اليوم
        existing = cls.get_by_student_group_date(student_id, group_id, attendance_date)
        
        if existing:
            # تحديث السجل الموجود
            existing.update(status=status, notes=notes, recorded_by=recorded_by)
            return existing
        
        # إنشاء سجل جديد
        attendance_data = {
            'student_id': student_id,
            'group_id': group_id,
            'date': attendance_date,
            'status': status,
            'notes': notes,
            'recorded_by': recorded_by,
            'recorded_at': datetime.now()
        }
        
        attendance_id = db_handler.insert('attendance', attendance_data)
        
        attendance = cls(
            attendance_id=attendance_id,
            student_id=student_id,
            group_id=group_id,
            date=attendance_date,
            status=status,
            notes=notes,
            recorded_by=recorded_by
        )
        
        attendance.logger.info(f"تم تسجيل حضور: طالب {student_id} - مجموعة {group_id} - {status}")
        return attendance
    
    @classmethod
    def get_by_id(cls, attendance_id: int) -> Optional['Attendance']:
        """الحصول على سجل حضور بالمعرف"""
        query = "SELECT * FROM attendance WHERE id = ?"
        row = db_handler.fetch_one(query, (attendance_id,))
        
        if row:
            return cls._from_row(row)
        return None
    
    @classmethod
    def get_by_student_group_date(cls, student_id: int, group_id: int,
                                  attendance_date: date) -> Optional['Attendance']:
        """الحصول على سجل حضور بالطالب والمجموعة والتاريخ"""
        query = "SELECT * FROM attendance WHERE student_id = ? AND group_id = ? AND date = ?"
        row = db_handler.fetch_one(query, (student_id, group_id, attendance_date))
        
        if row:
            return cls._from_row(row)
        return None
    
    @classmethod
    def get_by_student(cls, student_id: int, group_id: int = None,
                       start_date: date = None, end_date: date = None) -> List['Attendance']:
        """
        الحصول على سجلات حضور طالب
        
        Args:
            student_id: معرف الطالب
            group_id: معرف المجموعة (اختياري)
            start_date: تاريخ البداية (اختياري)
            end_date: تاريخ النهاية (اختياري)
            
        Returns:
            List[Attendance]: قائمة سجلات الحضور
        """
        query = "SELECT * FROM attendance WHERE student_id = ?"
        params = [student_id]
        
        if group_id:
            query += " AND group_id = ?"
            params.append(group_id)
        
        if start_date:
            query += " AND date >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND date <= ?"
            params.append(end_date)
        
        query += " ORDER BY date DESC"
        
        rows = db_handler.fetch_all(query, tuple(params))
        return [cls._from_row(row) for row in rows]
    
    @classmethod
    def get_by_group(cls, group_id: int, attendance_date: date = None,
                     start_date: date = None, end_date: date = None) -> List['Attendance']:
        """
        الحصول على سجلات حضور مجموعة
        
        Args:
            group_id: معرف المجموعة
            attendance_date: تاريخ محدد (اختياري)
            start_date: تاريخ البداية (اختياري)
            end_date: تاريخ النهاية (اختياري)
            
        Returns:
            List[Attendance]: قائمة سجلات الحضور
        """
        query = "SELECT * FROM attendance WHERE group_id = ?"
        params = [group_id]
        
        if attendance_date:
            query += " AND date = ?"
            params.append(attendance_date)
        elif start_date and end_date:
            query += " AND date BETWEEN ? AND ?"
            params.extend([start_date, end_date])
        elif start_date:
            query += " AND date >= ?"
            params.append(start_date)
        elif end_date:
            query += " AND date <= ?"
            params.append(end_date)
        
        query += " ORDER BY date DESC, student_id"
        
        rows = db_handler.fetch_all(query, tuple(params))
        return [cls._from_row(row) for row in rows]
    
    @classmethod
    def get_group_attendance_for_date(cls, group_id: int, attendance_date: date) -> List[Dict]:
        """
        الحصول على حضور المجموعة ليوم معين مع معلومات الطلاب
        
        Args:
            group_id: معرف المجموعة
            attendance_date: التاريخ
            
        Returns:
            List[Dict]: قائمة الحضور مع معلومات الطلاب
        """
        query = """
        SELECT s.id as student_id, s.student_code, s.first_name, s.last_name,
               a.status, a.notes, a.recorded_at
        FROM students s
        JOIN student_groups sg ON s.id = sg.student_id
        LEFT JOIN attendance a ON s.id = a.student_id AND a.group_id = ? AND a.date = ?
        WHERE sg.group_id = ? AND sg.status = 'active'
        ORDER BY s.first_name, s.last_name
        """
        
        rows = db_handler.fetch_all(query, (group_id, attendance_date, group_id))
        return [dict(row) for row in rows]
    
    @classmethod
    def record_group_attendance(cls, group_id: int, attendance_data: List[Dict],
                               attendance_date: date = None, recorded_by: int = None) -> bool:
        """
        تسجيل حضور مجموعة كاملة
        
        Args:
            group_id: معرف المجموعة
            attendance_data: بيانات الحضور [{'student_id': int, 'status': str, 'notes': str}]
            attendance_date: التاريخ
            recorded_by: معرف المسجل
            
        Returns:
            bool: نجح التسجيل أم لا
        """
        try:
            if not attendance_date:
                attendance_date = date.today()
            
            for data in attendance_data:
                student_id = data.get('student_id')
                status = data.get('status', 'absent')
                notes = data.get('notes')
                
                if student_id:
                    cls.record(
                        student_id=student_id,
                        group_id=group_id,
                        status=status,
                        attendance_date=attendance_date,
                        notes=notes,
                        recorded_by=recorded_by
                    )
            
            return True
        except Exception as e:
            logging.error(f"خطأ في تسجيل حضور المجموعة: {e}")
            return False
    
    def update(self, **kwargs) -> bool:
        """تحديث سجل الحضور"""
        try:
            # الحقول المسموح بتحديثها
            allowed_fields = ['status', 'notes', 'recorded_by']
            
            data = {}
            for key, value in kwargs.items():
                if key in allowed_fields:
                    data[key] = value
                    setattr(self, key, value)
            
            if data:
                data['recorded_at'] = datetime.now()
                rows_affected = db_handler.update('attendance', data, 'id = ?', (self.id,))
                
                if rows_affected > 0:
                    self.recorded_at = datetime.now()
                    self.logger.info(f"تم تحديث سجل الحضور: {self.id}")
                    return True
            
            return False
        except Exception as e:
            self.logger.error(f"خطأ في تحديث سجل الحضور: {e}")
            return False
    
    def delete(self) -> bool:
        """حذف سجل الحضور"""
        try:
            rows_affected = db_handler.delete('attendance', 'id = ?', (self.id,))
            
            if rows_affected > 0:
                self.logger.info(f"تم حذف سجل الحضور: {self.id}")
                return True
            
            return False
        except Exception as e:
            self.logger.error(f"خطأ في حذف سجل الحضور: {e}")
            return False
    
    @classmethod
    def get_student_summary(cls, student_id: int, group_id: int = None) -> Dict[str, Any]:
        """
        الحصول على ملخص حضور الطالب
        
        Args:
            student_id: معرف الطالب
            group_id: معرف المجموعة (اختياري)
            
        Returns:
            Dict: ملخص الحضور
        """
        query = "SELECT status, COUNT(*) as count FROM attendance WHERE student_id = ?"
        params = [student_id]
        
        if group_id:
            query += " AND group_id = ?"
            params.append(group_id)
        
        query += " GROUP BY status"
        
        rows = db_handler.fetch_all(query, tuple(params))
        
        summary = {status: 0 for status in cls.STATUSES.keys()}
        total = 0
        
        for row in rows:
            summary[row['status']] = row['count']
            total += row['count']
        
        summary['total'] = total
        summary['attendance_rate'] = (summary['present'] / total * 100) if total > 0 else 0
        
        return summary
    
    @classmethod
    def get_group_summary(cls, group_id: int, start_date: date = None,
                         end_date: date = None) -> Dict[str, Any]:
        """
        الحصول على ملخص حضور المجموعة
        
        Args:
            group_id: معرف المجموعة
            start_date: تاريخ البداية (اختياري)
            end_date: تاريخ النهاية (اختياري)
            
        Returns:
            Dict: ملخص الحضور
        """
        query = "SELECT status, COUNT(*) as count FROM attendance WHERE group_id = ?"
        params = [group_id]
        
        if start_date:
            query += " AND date >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND date <= ?"
            params.append(end_date)
        
        query += " GROUP BY status"
        
        rows = db_handler.fetch_all(query, tuple(params))
        
        summary = {status: 0 for status in cls.STATUSES.keys()}
        total = 0
        
        for row in rows:
            summary[row['status']] = row['count']
            total += row['count']
        
        summary['total'] = total
        summary['attendance_rate'] = (summary['present'] / total * 100) if total > 0 else 0
        
        return summary
    
    def get_student_info(self) -> Optional[Dict]:
        """الحصول على معلومات الطالب"""
        query = "SELECT * FROM students WHERE id = ?"
        row = db_handler.fetch_one(query, (self.student_id,))
        
        if row:
            return dict(row)
        return None
    
    def get_group_info(self) -> Optional[Dict]:
        """الحصول على معلومات المجموعة"""
        query = "SELECT * FROM groups WHERE id = ?"
        row = db_handler.fetch_one(query, (self.group_id,))
        
        if row:
            return dict(row)
        return None
    
    @classmethod
    def _from_row(cls, row) -> 'Attendance':
        """إنشاء كائن حضور من سجل قاعدة البيانات"""
        return cls(
            attendance_id=row['id'],
            student_id=row['student_id'],
            group_id=row['group_id'],
            date=date.fromisoformat(row['date']) if row['date'] else None,
            status=row['status'],
            notes=row['notes'],
            recorded_by=row['recorded_by'],
            recorded_at=datetime.fromisoformat(row['recorded_at']) if row['recorded_at'] else None
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل الحضور إلى قاموس"""
        return {
            'id': self.id,
            'student_id': self.student_id,
            'group_id': self.group_id,
            'date': self.date.isoformat() if self.date else None,
            'status': self.status,
            'status_name': self.STATUSES.get(self.status, self.status),
            'notes': self.notes,
            'recorded_by': self.recorded_by,
            'recorded_at': self.recorded_at.isoformat() if self.recorded_at else None
        }
    
    def __str__(self):
        return f"Attendance(id={self.id}, student={self.student_id}, group={self.group_id}, status='{self.status}')"
    
    def __repr__(self):
        return self.__str__()
