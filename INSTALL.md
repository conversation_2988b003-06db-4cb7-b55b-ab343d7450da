# دليل التثبيت والتشغيل
## Installation and Setup Guide

دليل شامل لتثبيت وتشغيل نظام إدارة الأكاديمية التعليمية

---

## 📋 المتطلبات الأساسية

### متطلبات النظام
- **نظام التشغيل:** Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **Python:** 3.8 أو أحدث
- **الذاكرة:** 4 GB RAM (الحد الأدنى)
- **التخزين:** 1 GB مساحة فارغة

### التحقق من Python
```bash
python --version
# أو
python3 --version
```

إذا لم يكن Python مثبتاً، قم بتحميله من: https://python.org

---

## 🚀 خطوات التثبيت

### 1. تحميل المشروع
```bash
# إذا كان لديك Git
git clone <repository-url>
cd academy-management-system

# أو قم بتحميل الملف المضغوط واستخراجه
```

### 2. إنشاء بيئة افتراضية (مستحسن)
```bash
# إنشاء البيئة الافتراضية
python -m venv venv

# تفعيل البيئة الافتراضية
# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate
```

### 3. تثبيت المتطلبات
```bash
# تحديث pip
python -m pip install --upgrade pip

# تثبيت المكتبات المطلوبة
pip install -r requirements.txt
```

### 4. إعداد النظام
```bash
# الطريقة الأولى: استخدام ملف الإعداد التفاعلي
python setup.py

# الطريقة الثانية: استخدام ملف التشغيل السريع
python run.py

# الطريقة الثالثة: إعداد يدوي
python -c "from database.db_handler import db_handler; db_handler.initialize_database()"
```

### 5. إنشاء بيانات تجريبية (اختياري)
```bash
python init_system.py
```

---

## 🎯 تشغيل النظام

### الطريقة الأولى: التشغيل المباشر
```bash
python main.py
```

### الطريقة الثانية: استخدام ملف التشغيل السريع
```bash
python run.py
```

### الطريقة الثالثة: استخدام ملفات النظام
```bash
# Windows
start.bat

# macOS/Linux
./start.sh
```

---

## 👤 بيانات تسجيل الدخول الافتراضية

### المدير
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

### المعلم (إذا تم إنشاء البيانات التجريبية)
- **اسم المستخدم:** `teacher1`
- **كلمة المرور:** `teacher123`

### الموظف (إذا تم إنشاء البيانات التجريبية)
- **اسم المستخدم:** `staff1`
- **كلمة المرور:** `staff123`

⚠️ **تحذير:** يرجى تغيير كلمات المرور الافتراضية فور تسجيل الدخول الأول.

---

## 🔧 استكشاف الأخطاء وإصلاحها

### مشكلة: Python غير موجود
```bash
# تأكد من تثبيت Python وإضافته إلى PATH
python --version
```

**الحل:** قم بتحميل وتثبيت Python من الموقع الرسمي.

### مشكلة: فشل في تثبيت المكتبات
```bash
# جرب تحديث pip أولاً
python -m pip install --upgrade pip

# ثم أعد تثبيت المكتبات
pip install -r requirements.txt
```

### مشكلة: خطأ في قاعدة البيانات
```bash
# احذف قاعدة البيانات الموجودة وأعد إنشاءها
rm database/academy_db.sqlite  # Linux/macOS
del database\academy_db.sqlite  # Windows

# ثم أعد تشغيل الإعداد
python setup.py
```

### مشكلة: خطأ في PyQt6
```bash
# تأكد من تثبيت PyQt6
pip install PyQt6

# إذا استمر الخطأ، جرب إعادة تثبيت
pip uninstall PyQt6
pip install PyQt6
```

### مشكلة: خطأ في الترميز (Encoding)
```bash
# تأكد من أن النظام يدعم UTF-8
# في Windows، قم بتشغيل:
chcp 65001
```

---

## 📁 هيكل الملفات المهمة

```
academy_management_system/
├── main.py                 # ملف التشغيل الرئيسي
├── run.py                  # ملف التشغيل السريع
├── setup.py                # ملف الإعداد التفاعلي
├── init_system.py          # إنشاء بيانات تجريبية
├── requirements.txt        # المتطلبات
├── config.ini              # ملف الإعدادات
├── start.bat               # تشغيل Windows
├── start.sh                # تشغيل Linux/macOS
│
├── database/               # قاعدة البيانات
│   ├── academy_db.sqlite   # ملف قاعدة البيانات
│   └── backup/             # النسخ الاحتياطية
│
├── logs/                   # ملفات السجلات
├── exports/                # ملفات التصدير
└── resources/              # الموارد
```

---

## 🔄 تحديث النظام

### تحديث المكتبات
```bash
pip install --upgrade -r requirements.txt
```

### تحديث قاعدة البيانات
```bash
# إنشاء نسخة احتياطية أولاً
python -c "from utils.backup import backup_manager; backup_manager.create_database_backup()"

# ثم تحديث المخطط
python -c "from database.db_handler import db_handler; db_handler.update_schema()"
```

---

## 💾 النسخ الاحتياطية

### إنشاء نسخة احتياطية يدوية
```bash
python -c "from utils.backup import backup_manager; print(backup_manager.create_database_backup())"
```

### استعادة نسخة احتياطية
```bash
python -c "from utils.backup import backup_manager; backup_manager.restore_database_backup('path/to/backup.sqlite')"
```

---

## 📊 اختبار النظام

### تشغيل الاختبارات الأساسية
```bash
python test_system.py
```

### تشغيل العرض التوضيحي
```bash
python demo.py
```

### اختبار سريع
```bash
python quick_test.py
```

---

## 🆘 الحصول على المساعدة

### سجلات النظام
تحقق من ملفات السجلات في مجلد `logs/`:
- `academy.log` - السجل الرئيسي
- `errors.log` - سجل الأخطاء
- `activity.log` - سجل النشاطات

### معلومات النظام
```bash
python -c "import sys; print(f'Python: {sys.version}'); import PyQt6; print('PyQt6: متاح')"
```

### إعادة تعيين النظام
```bash
# احذف جميع البيانات وأعد الإعداد
rm -rf database/academy_db.sqlite logs/* exports/*  # Linux/macOS
del database\academy_db.sqlite logs\* exports\*     # Windows

python setup.py
```

---

## 📞 الدعم الفني

- **البريد الإلكتروني:** <EMAIL>
- **الموقع:** https://academy-system.com
- **التوثيق:** https://docs.academy-system.com

---

## ✅ قائمة التحقق

- [ ] تم تثبيت Python 3.8+
- [ ] تم إنشاء البيئة الافتراضية
- [ ] تم تثبيت جميع المتطلبات
- [ ] تم إعداد قاعدة البيانات
- [ ] تم اختبار تسجيل الدخول
- [ ] تم تغيير كلمة المرور الافتراضية
- [ ] تم إنشاء نسخة احتياطية أولية

---

**© 2024 نظام إدارة الأكاديمية التعليمية. جميع الحقوق محفوظة.**
