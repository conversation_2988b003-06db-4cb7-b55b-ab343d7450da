"""
واجهة إدارة الطلاب المتقدمة
Advanced Student Management Interface
"""

import sys
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QTableWidget, QTableWidgetItem, QPushButton, QLineEdit,
    QLabel, QComboBox, QDateEdit, QTextEdit, QGroupBox,
    QHeaderView, QMessageBox, QDialog, QFormLayout,
    QDialogButtonBox, QTabWidget, QScrollArea, QFrame,
    QSplitter, QToolBar, QStatusBar, QProgressBar
)
from PyQt6.QtCore import Qt, QDate, pyqtSignal, QTimer
from PyQt6.QtGui import QIcon, QFont, QPixmap, QPalette, QColor

from models.student import Student
from models.user import User
from controllers.student_controller import StudentController
from utils.validators import DataValidator


class StudentFormDialog(QDialog):
    """نافذة حوار إضافة/تعديل طالب"""
    
    def __init__(self, parent=None, student=None, current_user=None):
        super().__init__(parent)
        self.student = student
        self.current_user = current_user
        self.controller = StudentController(current_user)
        
        self.setWindowTitle("إضافة طالب جديد" if not student else "تعديل بيانات الطالب")
        self.setModal(True)
        self.resize(500, 600)
        
        self.setup_ui()
        self.setup_style()
        
        if student:
            self.load_student_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # عنوان النافذة
        title_label = QLabel("إضافة طالب جديد" if not self.student else "تعديل بيانات الطالب")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # منطقة التمرير
        scroll = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # مجموعة البيانات الشخصية
        personal_group = QGroupBox("البيانات الشخصية")
        personal_layout = QFormLayout(personal_group)
        
        self.first_name_edit = QLineEdit()
        self.first_name_edit.setPlaceholderText("أدخل الاسم الأول")
        personal_layout.addRow("الاسم الأول:", self.first_name_edit)
        
        self.last_name_edit = QLineEdit()
        self.last_name_edit.setPlaceholderText("أدخل اسم العائلة")
        personal_layout.addRow("اسم العائلة:", self.last_name_edit)
        
        self.date_of_birth_edit = QDateEdit()
        self.date_of_birth_edit.setDate(QDate.currentDate().addYears(-10))
        self.date_of_birth_edit.setCalendarPopup(True)
        personal_layout.addRow("تاريخ الميلاد:", self.date_of_birth_edit)
        
        self.gender_combo = QComboBox()
        self.gender_combo.addItems(["ذكر", "أنثى"])
        personal_layout.addRow("الجنس:", self.gender_combo)
        
        scroll_layout.addWidget(personal_group)
        
        # مجموعة معلومات الاتصال
        contact_group = QGroupBox("معلومات الاتصال")
        contact_layout = QFormLayout(contact_group)
        
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("<EMAIL>")
        contact_layout.addRow("البريد الإلكتروني:", self.email_edit)
        
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("05xxxxxxxx")
        contact_layout.addRow("رقم الهاتف:", self.phone_edit)
        
        self.address_edit = QTextEdit()
        self.address_edit.setMaximumHeight(80)
        self.address_edit.setPlaceholderText("أدخل العنوان")
        contact_layout.addRow("العنوان:", self.address_edit)
        
        scroll_layout.addWidget(contact_group)
        
        # مجموعة بيانات ولي الأمر
        guardian_group = QGroupBox("بيانات ولي الأمر")
        guardian_layout = QFormLayout(guardian_group)
        
        self.guardian_name_edit = QLineEdit()
        self.guardian_name_edit.setPlaceholderText("اسم ولي الأمر")
        guardian_layout.addRow("اسم ولي الأمر:", self.guardian_name_edit)
        
        self.guardian_phone_edit = QLineEdit()
        self.guardian_phone_edit.setPlaceholderText("05xxxxxxxx")
        guardian_layout.addRow("هاتف ولي الأمر:", self.guardian_phone_edit)
        
        self.guardian_relation_combo = QComboBox()
        self.guardian_relation_combo.addItems(["الأب", "الأم", "الأخ", "الأخت", "الجد", "الجدة", "العم", "العمة", "الخال", "الخالة", "أخرى"])
        guardian_layout.addRow("صلة القرابة:", self.guardian_relation_combo)
        
        scroll_layout.addWidget(guardian_group)
        
        # مجموعة ملاحظات
        notes_group = QGroupBox("ملاحظات إضافية")
        notes_layout = QVBoxLayout(notes_group)
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        self.notes_edit.setPlaceholderText("أي ملاحظات إضافية...")
        notes_layout.addWidget(self.notes_edit)
        
        scroll_layout.addWidget(notes_group)
        
        scroll.setWidget(scroll_widget)
        scroll.setWidgetResizable(True)
        layout.addWidget(scroll)
        
        # أزرار الحفظ والإلغاء
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Save | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.save_student)
        button_box.rejected.connect(self.reject)
        
        # تخصيص النصوص
        button_box.button(QDialogButtonBox.StandardButton.Save).setText("حفظ")
        button_box.button(QDialogButtonBox.StandardButton.Cancel).setText("إلغاء")
        
        layout.addWidget(button_box)
    
    def setup_style(self):
        """إعداد التنسيق"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
            QLineEdit, QTextEdit, QComboBox, QDateEdit {
                padding: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                font-size: 12px;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QDateEdit:focus {
                border-color: #3498db;
                background-color: #ffffff;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
    
    def load_student_data(self):
        """تحميل بيانات الطالب للتعديل"""
        if not self.student:
            return
        
        self.first_name_edit.setText(self.student.first_name or "")
        self.last_name_edit.setText(self.student.last_name or "")
        
        if self.student.date_of_birth:
            self.date_of_birth_edit.setDate(QDate.fromString(str(self.student.date_of_birth), "yyyy-MM-dd"))
        
        if self.student.gender == "male":
            self.gender_combo.setCurrentText("ذكر")
        elif self.student.gender == "female":
            self.gender_combo.setCurrentText("أنثى")
        
        self.email_edit.setText(self.student.email or "")
        self.phone_edit.setText(self.student.phone or "")
        self.address_edit.setPlainText(self.student.address or "")
        self.guardian_name_edit.setText(self.student.guardian_name or "")
        self.guardian_phone_edit.setText(self.student.guardian_phone or "")
        self.notes_edit.setPlainText(self.student.notes or "")
    
    def save_student(self):
        """حفظ بيانات الطالب"""
        # جمع البيانات
        data = {
            'first_name': self.first_name_edit.text().strip(),
            'last_name': self.last_name_edit.text().strip(),
            'date_of_birth': self.date_of_birth_edit.date().toPython(),
            'gender': 'male' if self.gender_combo.currentText() == 'ذكر' else 'female',
            'email': self.email_edit.text().strip(),
            'phone': self.phone_edit.text().strip(),
            'address': self.address_edit.toPlainText().strip(),
            'guardian_name': self.guardian_name_edit.text().strip(),
            'guardian_phone': self.guardian_phone_edit.text().strip(),
            'guardian_relation': self.guardian_relation_combo.currentText(),
            'notes': self.notes_edit.toPlainText().strip()
        }
        
        # التحقق من البيانات المطلوبة
        if not data['first_name'] or not data['last_name']:
            QMessageBox.warning(self, "خطأ", "الاسم الأول واسم العائلة مطلوبان")
            return
        
        try:
            if self.student:
                # تعديل طالب موجود
                result = self.controller.update_student(self.student.id, data)
            else:
                # إضافة طالب جديد
                result = self.controller.create_student(data)
            
            if result['success']:
                QMessageBox.information(self, "نجح", result['message'])
                self.accept()
            else:
                QMessageBox.warning(self, "خطأ", result['message'])
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")


class StudentManagementWidget(QWidget):
    """واجهة إدارة الطلاب الرئيسية"""
    
    def __init__(self, current_user=None):
        super().__init__()
        self.current_user = current_user
        self.controller = StudentController(current_user)
        self.students_data = []
        
        self.setup_ui()
        self.setup_style()
        self.load_students()
        
        # تحديث البيانات كل 30 ثانية
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_students)
        self.refresh_timer.start(30000)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # شريط الأدوات العلوي
        toolbar_layout = QHBoxLayout()
        
        # عنوان الصفحة
        title_label = QLabel("إدارة الطلاب")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
            }
        """)
        toolbar_layout.addWidget(title_label)
        
        toolbar_layout.addStretch()
        
        # أزرار العمليات
        self.add_btn = QPushButton("إضافة طالب جديد")
        self.add_btn.clicked.connect(self.add_student)
        toolbar_layout.addWidget(self.add_btn)
        
        self.edit_btn = QPushButton("تعديل")
        self.edit_btn.clicked.connect(self.edit_student)
        self.edit_btn.setEnabled(False)
        toolbar_layout.addWidget(self.edit_btn)
        
        self.delete_btn = QPushButton("حذف")
        self.delete_btn.clicked.connect(self.delete_student)
        self.delete_btn.setEnabled(False)
        toolbar_layout.addWidget(self.delete_btn)
        
        self.refresh_btn = QPushButton("تحديث")
        self.refresh_btn.clicked.connect(self.load_students)
        toolbar_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(toolbar_layout)
        
        # شريط البحث والتصفية
        search_layout = QHBoxLayout()
        
        search_layout.addWidget(QLabel("البحث:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث بالاسم أو رقم الطالب أو البريد الإلكتروني...")
        self.search_edit.textChanged.connect(self.filter_students)
        search_layout.addWidget(self.search_edit)
        
        search_layout.addWidget(QLabel("الحالة:"))
        self.status_filter = QComboBox()
        self.status_filter.addItems(["الكل", "نشط", "غير نشط", "متخرج"])
        self.status_filter.currentTextChanged.connect(self.filter_students)
        search_layout.addWidget(self.status_filter)
        
        search_layout.addWidget(QLabel("الجنس:"))
        self.gender_filter = QComboBox()
        self.gender_filter.addItems(["الكل", "ذكر", "أنثى"])
        self.gender_filter.currentTextChanged.connect(self.filter_students)
        search_layout.addWidget(self.gender_filter)
        
        layout.addLayout(search_layout)
        
        # جدول الطلاب
        self.students_table = QTableWidget()
        self.students_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.students_table.setAlternatingRowColors(True)
        self.students_table.setSortingEnabled(True)
        self.students_table.selectionModel().selectionChanged.connect(self.on_selection_changed)
        self.students_table.doubleClicked.connect(self.edit_student)
        
        # إعداد أعمدة الجدول
        headers = ["رقم الطالب", "الاسم الكامل", "العمر", "الجنس", "البريد الإلكتروني", "الهاتف", "الحالة", "تاريخ التسجيل"]
        self.students_table.setColumnCount(len(headers))
        self.students_table.setHorizontalHeaderLabels(headers)
        
        # تخصيص عرض الأعمدة
        header = self.students_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # رقم الطالب
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # الاسم
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # العمر
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # الجنس
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)  # البريد
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # الهاتف
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # الحالة
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.ResizeToContents)  # تاريخ التسجيل
        
        layout.addWidget(self.students_table)
        
        # شريط الحالة
        status_layout = QHBoxLayout()
        self.status_label = QLabel("جاري التحميل...")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        self.count_label = QLabel("عدد الطلاب: 0")
        status_layout.addWidget(self.count_label)
        
        layout.addLayout(status_layout)
    
    def setup_style(self):
        """إعداد التنسيق"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
            QLineEdit, QComboBox {
                padding: 6px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #3498db;
            }
            QTableWidget {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
    
    def load_students(self):
        """تحميل قائمة الطلاب"""
        try:
            self.status_label.setText("جاري تحميل البيانات...")
            
            result = self.controller.search_students({})
            
            if result['success']:
                self.students_data = result['students']
                self.populate_table()
                self.status_label.setText("تم تحميل البيانات بنجاح")
                self.count_label.setText(f"عدد الطلاب: {len(self.students_data)}")
            else:
                self.status_label.setText(f"خطأ: {result['message']}")
                QMessageBox.warning(self, "خطأ", result['message'])
                
        except Exception as e:
            self.status_label.setText("خطأ في تحميل البيانات")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل البيانات: {str(e)}")
    
    def populate_table(self):
        """ملء الجدول بالبيانات"""
        filtered_data = self.get_filtered_data()
        
        self.students_table.setRowCount(len(filtered_data))
        
        for row, student in enumerate(filtered_data):
            # رقم الطالب
            self.students_table.setItem(row, 0, QTableWidgetItem(student.get('student_code', '')))
            
            # الاسم الكامل
            full_name = f"{student.get('first_name', '')} {student.get('last_name', '')}"
            self.students_table.setItem(row, 1, QTableWidgetItem(full_name))
            
            # العمر
            age = str(student.get('age', ''))
            self.students_table.setItem(row, 2, QTableWidgetItem(age))
            
            # الجنس
            gender = "ذكر" if student.get('gender') == 'male' else "أنثى"
            self.students_table.setItem(row, 3, QTableWidgetItem(gender))
            
            # البريد الإلكتروني
            self.students_table.setItem(row, 4, QTableWidgetItem(student.get('email', '')))
            
            # الهاتف
            self.students_table.setItem(row, 5, QTableWidgetItem(student.get('phone', '')))
            
            # الحالة
            status = student.get('status', 'active')
            status_text = {"active": "نشط", "inactive": "غير نشط", "graduated": "متخرج"}.get(status, status)
            self.students_table.setItem(row, 6, QTableWidgetItem(status_text))
            
            # تاريخ التسجيل
            created_at = student.get('created_at', '')
            if created_at:
                try:
                    from datetime import datetime
                    date_obj = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    formatted_date = date_obj.strftime('%Y-%m-%d')
                except:
                    formatted_date = created_at
            else:
                formatted_date = ''
            self.students_table.setItem(row, 7, QTableWidgetItem(formatted_date))
    
    def get_filtered_data(self):
        """الحصول على البيانات المفلترة"""
        filtered_data = self.students_data.copy()
        
        # تصفية النص
        search_text = self.search_edit.text().lower()
        if search_text:
            filtered_data = [
                student for student in filtered_data
                if (search_text in student.get('student_code', '').lower() or
                    search_text in f"{student.get('first_name', '')} {student.get('last_name', '')}".lower() or
                    search_text in student.get('email', '').lower())
            ]
        
        # تصفية الحالة
        status_filter = self.status_filter.currentText()
        if status_filter != "الكل":
            status_map = {"نشط": "active", "غير نشط": "inactive", "متخرج": "graduated"}
            status_value = status_map.get(status_filter)
            if status_value:
                filtered_data = [
                    student for student in filtered_data
                    if student.get('status') == status_value
                ]
        
        # تصفية الجنس
        gender_filter = self.gender_filter.currentText()
        if gender_filter != "الكل":
            gender_value = "male" if gender_filter == "ذكر" else "female"
            filtered_data = [
                student for student in filtered_data
                if student.get('gender') == gender_value
            ]
        
        return filtered_data
    
    def filter_students(self):
        """تصفية الطلاب"""
        self.populate_table()
        filtered_count = self.students_table.rowCount()
        total_count = len(self.students_data)
        self.count_label.setText(f"عدد الطلاب: {filtered_count} من {total_count}")
    
    def on_selection_changed(self):
        """عند تغيير التحديد"""
        has_selection = len(self.students_table.selectionModel().selectedRows()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection and self.current_user.role == 'admin')
    
    def add_student(self):
        """إضافة طالب جديد"""
        dialog = StudentFormDialog(self, current_user=self.current_user)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.load_students()
    
    def edit_student(self):
        """تعديل طالب"""
        selected_rows = self.students_table.selectionModel().selectedRows()
        if not selected_rows:
            return
        
        row = selected_rows[0].row()
        student_code = self.students_table.item(row, 0).text()
        
        # البحث عن الطالب
        student_data = None
        for student in self.students_data:
            if student.get('student_code') == student_code:
                student_data = student
                break
        
        if student_data:
            # تحويل البيانات إلى كائن Student مؤقت
            class TempStudent:
                def __init__(self, data):
                    for key, value in data.items():
                        setattr(self, key, value)
            
            temp_student = TempStudent(student_data)
            dialog = StudentFormDialog(self, temp_student, self.current_user)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.load_students()
    
    def delete_student(self):
        """حذف طالب"""
        if self.current_user.role != 'admin':
            QMessageBox.warning(self, "خطأ", "ليس لديك صلاحية لحذف الطلاب")
            return
        
        selected_rows = self.students_table.selectionModel().selectedRows()
        if not selected_rows:
            return
        
        row = selected_rows[0].row()
        student_name = self.students_table.item(row, 1).text()
        student_code = self.students_table.item(row, 0).text()
        
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف الطالب:\n{student_name} ({student_code})؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # البحث عن معرف الطالب
            student_id = None
            for student in self.students_data:
                if student.get('student_code') == student_code:
                    student_id = student.get('id')
                    break
            
            if student_id:
                result = self.controller.delete_student(student_id)
                if result['success']:
                    QMessageBox.information(self, "نجح", result['message'])
                    self.load_students()
                else:
                    QMessageBox.warning(self, "خطأ", result['message'])
