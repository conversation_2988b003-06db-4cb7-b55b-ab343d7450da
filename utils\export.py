"""
أدوات التصدير - Export Utilities
"""

import os
import csv
import json
import logging
from datetime import datetime, date
from typing import List, Dict, Any, Optional
from pathlib import Path

try:
    import openpyxl
    from openpyxl.styles import Font, Alignment, PatternFill
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False

try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib import colors
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

class ExportManager:
    """مدير التصدير"""
    
    def __init__(self, export_dir: str = "exports"):
        """
        تهيئة مدير التصدير
        
        Args:
            export_dir: مجلد التصدير
        """
        self.export_dir = Path(export_dir)
        self.export_dir.mkdir(exist_ok=True)
        self.logger = logging.getLogger(__name__)
    
    def export_to_csv(self, data: List[Dict[str, Any]], filename: str, 
                     headers: Optional[List[str]] = None) -> str:
        """
        تصدير البيانات إلى ملف CSV
        
        Args:
            data: البيانات المراد تصديرها
            filename: اسم الملف
            headers: رؤوس الأعمدة
            
        Returns:
            str: مسار الملف المُصدر
        """
        try:
            if not filename.endswith('.csv'):
                filename += '.csv'
            
            filepath = self.export_dir / filename
            
            if not data:
                raise ValueError("لا توجد بيانات للتصدير")
            
            # استخدام رؤوس الأعمدة من البيانات إذا لم يتم تمريرها
            if not headers:
                headers = list(data[0].keys())
            
            with open(filepath, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=headers)
                writer.writeheader()
                
                for row in data:
                    # تنظيف البيانات
                    clean_row = {}
                    for header in headers:
                        value = row.get(header, '')
                        if isinstance(value, (date, datetime)):
                            value = value.isoformat()
                        elif value is None:
                            value = ''
                        clean_row[header] = str(value)
                    
                    writer.writerow(clean_row)
            
            self.logger.info(f"تم تصدير {len(data)} سجل إلى ملف CSV: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"خطأ في تصدير CSV: {e}")
            raise
    
    def export_to_excel(self, data: List[Dict[str, Any]], filename: str,
                       sheet_name: str = "البيانات", headers: Optional[List[str]] = None) -> str:
        """
        تصدير البيانات إلى ملف Excel
        
        Args:
            data: البيانات المراد تصديرها
            filename: اسم الملف
            sheet_name: اسم الورقة
            headers: رؤوس الأعمدة
            
        Returns:
            str: مسار الملف المُصدر
        """
        if not EXCEL_AVAILABLE:
            raise ImportError("مكتبة openpyxl غير مثبتة")
        
        try:
            if not filename.endswith('.xlsx'):
                filename += '.xlsx'
            
            filepath = self.export_dir / filename
            
            if not data:
                raise ValueError("لا توجد بيانات للتصدير")
            
            # إنشاء مصنف جديد
            workbook = openpyxl.Workbook()
            worksheet = workbook.active
            worksheet.title = sheet_name
            
            # استخدام رؤوس الأعمدة من البيانات إذا لم يتم تمريرها
            if not headers:
                headers = list(data[0].keys())
            
            # تنسيق رؤوس الأعمدة
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")
            
            # كتابة رؤوس الأعمدة
            for col, header in enumerate(headers, 1):
                cell = worksheet.cell(row=1, column=col, value=header)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
            
            # كتابة البيانات
            for row_idx, row_data in enumerate(data, 2):
                for col_idx, header in enumerate(headers, 1):
                    value = row_data.get(header, '')
                    
                    if isinstance(value, (date, datetime)):
                        value = value.isoformat()
                    elif value is None:
                        value = ''
                    
                    cell = worksheet.cell(row=row_idx, column=col_idx, value=str(value))
                    cell.alignment = Alignment(horizontal="right")
            
            # تعديل عرض الأعمدة
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
            
            # حفظ الملف
            workbook.save(filepath)
            
            self.logger.info(f"تم تصدير {len(data)} سجل إلى ملف Excel: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"خطأ في تصدير Excel: {e}")
            raise
    
    def export_to_json(self, data: List[Dict[str, Any]], filename: str) -> str:
        """
        تصدير البيانات إلى ملف JSON
        
        Args:
            data: البيانات المراد تصديرها
            filename: اسم الملف
            
        Returns:
            str: مسار الملف المُصدر
        """
        try:
            if not filename.endswith('.json'):
                filename += '.json'
            
            filepath = self.export_dir / filename
            
            # تنظيف البيانات للتسلسل JSON
            clean_data = []
            for row in data:
                clean_row = {}
                for key, value in row.items():
                    if isinstance(value, (date, datetime)):
                        value = value.isoformat()
                    elif value is None:
                        value = None
                    clean_row[key] = value
                clean_data.append(clean_row)
            
            with open(filepath, 'w', encoding='utf-8') as jsonfile:
                json.dump(clean_data, jsonfile, ensure_ascii=False, indent=2)
            
            self.logger.info(f"تم تصدير {len(data)} سجل إلى ملف JSON: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"خطأ في تصدير JSON: {e}")
            raise
    
    def export_to_pdf(self, data: List[Dict[str, Any]], filename: str,
                     title: str = "تقرير", headers: Optional[List[str]] = None) -> str:
        """
        تصدير البيانات إلى ملف PDF
        
        Args:
            data: البيانات المراد تصديرها
            filename: اسم الملف
            title: عنوان التقرير
            headers: رؤوس الأعمدة
            
        Returns:
            str: مسار الملف المُصدر
        """
        if not PDF_AVAILABLE:
            raise ImportError("مكتبة reportlab غير مثبتة")
        
        try:
            if not filename.endswith('.pdf'):
                filename += '.pdf'
            
            filepath = self.export_dir / filename
            
            if not data:
                raise ValueError("لا توجد بيانات للتصدير")
            
            # إنشاء مستند PDF
            doc = SimpleDocTemplate(str(filepath), pagesize=A4)
            story = []
            
            # الأنماط
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1  # وسط
            )
            
            # العنوان
            story.append(Paragraph(title, title_style))
            story.append(Spacer(1, 12))
            
            # استخدام رؤوس الأعمدة من البيانات إذا لم يتم تمريرها
            if not headers:
                headers = list(data[0].keys())
            
            # إعداد بيانات الجدول
            table_data = [headers]  # رؤوس الأعمدة
            
            for row in data:
                row_data = []
                for header in headers:
                    value = row.get(header, '')
                    if isinstance(value, (date, datetime)):
                        value = value.strftime('%Y-%m-%d')
                    elif value is None:
                        value = ''
                    row_data.append(str(value))
                table_data.append(row_data)
            
            # إنشاء الجدول
            table = Table(table_data)
            
            # تنسيق الجدول
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(table)
            
            # بناء المستند
            doc.build(story)
            
            self.logger.info(f"تم تصدير {len(data)} سجل إلى ملف PDF: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"خطأ في تصدير PDF: {e}")
            raise
    
    def export_students_report(self, students_data: List[Dict], format: str = 'excel') -> str:
        """تصدير تقرير الطلاب"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"students_report_{timestamp}"
        
        headers = [
            'رمز الطالب', 'الاسم الأول', 'اسم العائلة', 'البريد الإلكتروني',
            'رقم الهاتف', 'تاريخ التسجيل', 'الحالة'
        ]
        
        if format.lower() == 'csv':
            return self.export_to_csv(students_data, filename, headers)
        elif format.lower() == 'excel':
            return self.export_to_excel(students_data, filename, "تقرير الطلاب", headers)
        elif format.lower() == 'pdf':
            return self.export_to_pdf(students_data, filename, "تقرير الطلاب", headers)
        elif format.lower() == 'json':
            return self.export_to_json(students_data, filename)
        else:
            raise ValueError(f"تنسيق غير مدعوم: {format}")
    
    def export_attendance_report(self, attendance_data: List[Dict], format: str = 'excel') -> str:
        """تصدير تقرير الحضور"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"attendance_report_{timestamp}"
        
        headers = [
            'اسم الطالب', 'المجموعة', 'التاريخ', 'حالة الحضور', 'ملاحظات'
        ]
        
        if format.lower() == 'csv':
            return self.export_to_csv(attendance_data, filename, headers)
        elif format.lower() == 'excel':
            return self.export_to_excel(attendance_data, filename, "تقرير الحضور", headers)
        elif format.lower() == 'pdf':
            return self.export_to_pdf(attendance_data, filename, "تقرير الحضور", headers)
        elif format.lower() == 'json':
            return self.export_to_json(attendance_data, filename)
        else:
            raise ValueError(f"تنسيق غير مدعوم: {format}")
    
    def export_financial_report(self, financial_data: List[Dict], format: str = 'excel') -> str:
        """تصدير التقرير المالي"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"financial_report_{timestamp}"
        
        headers = [
            'اسم الطالب', 'المبلغ', 'نوع الدفعة', 'تاريخ الدفع', 'الحالة', 'رقم الإيصال'
        ]
        
        if format.lower() == 'csv':
            return self.export_to_csv(financial_data, filename, headers)
        elif format.lower() == 'excel':
            return self.export_to_excel(financial_data, filename, "التقرير المالي", headers)
        elif format.lower() == 'pdf':
            return self.export_to_pdf(financial_data, filename, "التقرير المالي", headers)
        elif format.lower() == 'json':
            return self.export_to_json(financial_data, filename)
        else:
            raise ValueError(f"تنسيق غير مدعوم: {format}")
    
    def get_available_formats(self) -> List[str]:
        """الحصول على التنسيقات المتاحة"""
        formats = ['csv', 'json']
        
        if EXCEL_AVAILABLE:
            formats.append('excel')
        
        if PDF_AVAILABLE:
            formats.append('pdf')
        
        return formats
