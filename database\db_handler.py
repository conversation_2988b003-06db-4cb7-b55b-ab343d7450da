"""
نظام إدارة الأكاديمية التعليمية - معالج قاعدة البيانات
Academy Management System - Database Handler
"""

import sqlite3
import os
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
import json
import shutil
from pathlib import Path

class DatabaseHandler:
    """معالج قاعدة البيانات الرئيسي"""

    def __init__(self, db_path: str = "database/academy_db.sqlite"):
        """
        تهيئة معالج قاعدة البيانات

        Args:
            db_path: مسار ملف قاعدة البيانات
        """
        self.db_path = db_path
        self.connection = None
        self.logger = logging.getLogger(__name__)

        # إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
        os.makedirs(os.path.dirname(db_path), exist_ok=True)

        # تهيئة قاعدة البيانات
        self.initialize_database()

    def get_connection(self) -> sqlite3.Connection:
        """الحصول على اتصال بقاعدة البيانات"""
        if self.connection is None:
            self.connection = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30.0
            )
            self.connection.row_factory = sqlite3.Row
            # تفعيل Foreign Keys
            self.connection.execute("PRAGMA foreign_keys = ON")
        return self.connection

    def initialize_database(self):
        """تهيئة قاعدة البيانات وإنشاء الجداول"""
        try:
            # قراءة ملف المخطط
            schema_path = "database/schema.sql"
            if os.path.exists(schema_path):
                with open(schema_path, 'r', encoding='utf-8') as f:
                    schema_sql = f.read()

                conn = self.get_connection()
                conn.executescript(schema_sql)
                conn.commit()

                self.logger.info("تم تهيئة قاعدة البيانات بنجاح")
            else:
                self.logger.error(f"ملف المخطط غير موجود: {schema_path}")

        except Exception as e:
            self.logger.error(f"خطأ في تهيئة قاعدة البيانات: {e}")
            raise

    def execute_query(self, query: str, params: tuple = None) -> sqlite3.Cursor:
        """
        تنفيذ استعلام SQL

        Args:
            query: الاستعلام
            params: المعاملات

        Returns:
            cursor: مؤشر النتائج
        """
        try:
            conn = self.get_connection()
            if params:
                cursor = conn.execute(query, params)
            else:
                cursor = conn.execute(query)
            conn.commit()
            return cursor
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            raise

    def fetch_one(self, query: str, params: tuple = None) -> Optional[sqlite3.Row]:
        """جلب سجل واحد"""
        cursor = self.execute_query(query, params)
        return cursor.fetchone()

    def fetch_all(self, query: str, params: tuple = None) -> List[sqlite3.Row]:
        """جلب جميع السجلات"""
        cursor = self.execute_query(query, params)
        return cursor.fetchall()

    def insert(self, table: str, data: Dict[str, Any]) -> int:
        """
        إدراج سجل جديد

        Args:
            table: اسم الجدول
            data: البيانات المراد إدراجها

        Returns:
            int: معرف السجل الجديد
        """
        columns = ', '.join(data.keys())
        placeholders = ', '.join(['?' for _ in data])
        query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"

        # تحويل Decimal إلى float لـ SQLite
        values = []
        for value in data.values():
            if hasattr(value, '__class__') and value.__class__.__name__ == 'Decimal':
                values.append(float(value))
            else:
                values.append(value)

        cursor = self.execute_query(query, tuple(values))
        return cursor.lastrowid

    def update(self, table: str, data: Dict[str, Any], where_clause: str, where_params: tuple = None) -> int:
        """
        تحديث سجل

        Args:
            table: اسم الجدول
            data: البيانات المراد تحديثها
            where_clause: شرط التحديث
            where_params: معاملات الشرط

        Returns:
            int: عدد السجلات المحدثة
        """
        set_clause = ', '.join([f"{key} = ?" for key in data.keys()])
        query = f"UPDATE {table} SET {set_clause} WHERE {where_clause}"

        # تحويل Decimal إلى float لـ SQLite
        params = []
        for value in data.values():
            if hasattr(value, '__class__') and value.__class__.__name__ == 'Decimal':
                params.append(float(value))
            else:
                params.append(value)

        if where_params:
            params.extend(where_params)

        cursor = self.execute_query(query, tuple(params))
        return cursor.rowcount

    def delete(self, table: str, where_clause: str, where_params: tuple = None) -> int:
        """
        حذف سجل

        Args:
            table: اسم الجدول
            where_clause: شرط الحذف
            where_params: معاملات الشرط

        Returns:
            int: عدد السجلات المحذوفة
        """
        query = f"DELETE FROM {table} WHERE {where_clause}"
        cursor = self.execute_query(query, where_params)
        return cursor.rowcount

    def backup_database(self, backup_path: str = None) -> str:
        """
        إنشاء نسخة احتياطية من قاعدة البيانات

        Args:
            backup_path: مسار النسخة الاحتياطية

        Returns:
            str: مسار النسخة الاحتياطية
        """
        if backup_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"database/backup/academy_backup_{timestamp}.sqlite"

        # إنشاء مجلد النسخ الاحتياطية
        os.makedirs(os.path.dirname(backup_path), exist_ok=True)

        try:
            shutil.copy2(self.db_path, backup_path)
            self.logger.info(f"تم إنشاء نسخة احتياطية: {backup_path}")
            return backup_path
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            raise

    def restore_database(self, backup_path: str):
        """
        استعادة قاعدة البيانات من نسخة احتياطية

        Args:
            backup_path: مسار النسخة الاحتياطية
        """
        try:
            if os.path.exists(backup_path):
                # إغلاق الاتصال الحالي
                if self.connection:
                    self.connection.close()
                    self.connection = None

                # استعادة النسخة الاحتياطية
                shutil.copy2(backup_path, self.db_path)
                self.logger.info(f"تم استعادة قاعدة البيانات من: {backup_path}")
            else:
                raise FileNotFoundError(f"النسخة الاحتياطية غير موجودة: {backup_path}")
        except Exception as e:
            self.logger.error(f"خطأ في استعادة قاعدة البيانات: {e}")
            raise

    def get_table_info(self, table_name: str) -> List[Dict]:
        """الحصول على معلومات الجدول"""
        query = f"PRAGMA table_info({table_name})"
        rows = self.fetch_all(query)
        return [dict(row) for row in rows]

    def get_all_tables(self) -> List[str]:
        """الحصول على قائمة بجميع الجداول"""
        query = "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
        rows = self.fetch_all(query)
        return [row['name'] for row in rows]

    def close(self):
        """إغلاق اتصال قاعدة البيانات"""
        if self.connection:
            self.connection.close()
            self.connection = None

    def __enter__(self):
        """دعم context manager"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """إغلاق الاتصال عند الخروج"""
        self.close()

# إنشاء مثيل عام لمعالج قاعدة البيانات
db_handler = DatabaseHandler()
