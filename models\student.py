"""
نموذج الطالب - Student Model
"""

import logging
from datetime import datetime, date
from typing import Optional, Dict, Any, List
from database.db_handler import db_handler

class Student:
    """نموذج الطالب"""
    
    # حالات الطالب المتاحة
    STATUSES = {
        'active': 'نشط',
        'suspended': 'موقوف',
        'graduated': 'متخرج',
        'withdrawn': 'منسحب'
    }
    
    # الجنس
    GENDERS = {
        'male': 'ذكر',
        'female': 'أنثى'
    }
    
    def __init__(self, student_id: int = None, user_id: int = None,
                 student_code: str = None, first_name: str = None,
                 last_name: str = None, date_of_birth: date = None,
                 gender: str = None, phone: str = None, email: str = None,
                 address: str = None, guardian_name: str = None,
                 guardian_phone: str = None, emergency_contact: str = None,
                 enrollment_date: date = None, status: str = 'active',
                 notes: str = None, photo_path: str = None,
                 created_at: datetime = None, updated_at: datetime = None):
        """
        تهيئة نموذج الطالب
        """
        self.id = student_id
        self.user_id = user_id
        self.student_code = student_code
        self.first_name = first_name
        self.last_name = last_name
        self.date_of_birth = date_of_birth
        self.gender = gender
        self.phone = phone
        self.email = email
        self.address = address
        self.guardian_name = guardian_name
        self.guardian_phone = guardian_phone
        self.emergency_contact = emergency_contact
        self.enrollment_date = enrollment_date or date.today()
        self.status = status
        self.notes = notes
        self.photo_path = photo_path
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()
        self.logger = logging.getLogger(__name__)
    
    @property
    def full_name(self) -> str:
        """الاسم الكامل"""
        return f"{self.first_name} {self.last_name}".strip()
    
    @property
    def age(self) -> Optional[int]:
        """العمر"""
        if self.date_of_birth:
            today = date.today()
            return today.year - self.date_of_birth.year - (
                (today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day)
            )
        return None
    
    @classmethod
    def create(cls, first_name: str, last_name: str, student_code: str = None,
               **kwargs) -> 'Student':
        """
        إنشاء طالب جديد
        
        Args:
            first_name: الاسم الأول
            last_name: اسم العائلة
            student_code: رمز الطالب
            **kwargs: باقي البيانات
            
        Returns:
            Student: الطالب الجديد
        """
        # التحقق من صحة البيانات
        if not first_name or not last_name:
            raise ValueError("الاسم الأول واسم العائلة مطلوبان")
        
        # إنشاء رمز الطالب إذا لم يتم تمريره
        if not student_code:
            student_code = cls._generate_student_code()
        
        # التحقق من عدم وجود رمز الطالب مسبقاً
        if cls.get_by_code(student_code):
            raise ValueError("رمز الطالب موجود مسبقاً")
        
        # إعداد البيانات
        student_data = {
            'student_code': student_code,
            'first_name': first_name,
            'last_name': last_name,
            'enrollment_date': date.today(),
            'status': 'active',
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
        
        # إضافة البيانات الإضافية
        allowed_fields = [
            'user_id', 'date_of_birth', 'gender', 'phone', 'email',
            'address', 'guardian_name', 'guardian_phone', 'emergency_contact',
            'notes', 'photo_path'
        ]
        
        for field in allowed_fields:
            if field in kwargs and kwargs[field] is not None:
                student_data[field] = kwargs[field]
        
        # إدراج في قاعدة البيانات
        student_id = db_handler.insert('students', student_data)
        
        # إنشاء كائن الطالب
        student = cls(
            student_id=student_id,
            student_code=student_code,
            first_name=first_name,
            last_name=last_name,
            **kwargs
        )
        
        student.logger.info(f"تم إنشاء طالب جديد: {student.full_name} ({student_code})")
        return student
    
    @classmethod
    def get_by_id(cls, student_id: int) -> Optional['Student']:
        """الحصول على طالب بالمعرف"""
        query = "SELECT * FROM students WHERE id = ?"
        row = db_handler.fetch_one(query, (student_id,))
        
        if row:
            return cls._from_row(row)
        return None
    
    @classmethod
    def get_by_code(cls, student_code: str) -> Optional['Student']:
        """الحصول على طالب بالرمز"""
        query = "SELECT * FROM students WHERE student_code = ?"
        row = db_handler.fetch_one(query, (student_code,))
        
        if row:
            return cls._from_row(row)
        return None
    
    @classmethod
    def get_by_user_id(cls, user_id: int) -> Optional['Student']:
        """الحصول على طالب بمعرف المستخدم"""
        query = "SELECT * FROM students WHERE user_id = ?"
        row = db_handler.fetch_one(query, (user_id,))
        
        if row:
            return cls._from_row(row)
        return None
    
    @classmethod
    def search(cls, search_term: str = None, status: str = None,
               gender: str = None, limit: int = None) -> List['Student']:
        """
        البحث عن الطلاب
        
        Args:
            search_term: مصطلح البحث (اسم أو رمز)
            status: الحالة
            gender: الجنس
            limit: عدد النتائج المحدود
            
        Returns:
            List[Student]: قائمة الطلاب
        """
        query = "SELECT * FROM students WHERE 1=1"
        params = []
        
        if search_term:
            query += " AND (first_name LIKE ? OR last_name LIKE ? OR student_code LIKE ?)"
            search_pattern = f"%{search_term}%"
            params.extend([search_pattern, search_pattern, search_pattern])
        
        if status:
            query += " AND status = ?"
            params.append(status)
        
        if gender:
            query += " AND gender = ?"
            params.append(gender)
        
        query += " ORDER BY first_name, last_name"
        
        if limit:
            query += f" LIMIT {limit}"
        
        rows = db_handler.fetch_all(query, tuple(params) if params else None)
        return [cls._from_row(row) for row in rows]
    
    @classmethod
    def get_all(cls, status: str = None) -> List['Student']:
        """الحصول على جميع الطلاب"""
        return cls.search(status=status)
    
    @classmethod
    def _generate_student_code(cls) -> str:
        """إنشاء رمز طالب جديد"""
        # الحصول على آخر رمز طالب
        query = "SELECT student_code FROM students ORDER BY id DESC LIMIT 1"
        row = db_handler.fetch_one(query)
        
        if row and row['student_code']:
            # استخراج الرقم من آخر رمز
            last_code = row['student_code']
            if last_code.startswith('STU'):
                try:
                    last_number = int(last_code[3:])
                    new_number = last_number + 1
                except ValueError:
                    new_number = 1
            else:
                new_number = 1
        else:
            new_number = 1
        
        return f"STU{new_number:06d}"  # STU000001, STU000002, etc.
    
    def update(self, **kwargs) -> bool:
        """تحديث بيانات الطالب"""
        try:
            # الحقول المسموح بتحديثها
            allowed_fields = [
                'first_name', 'last_name', 'date_of_birth', 'gender',
                'phone', 'email', 'address', 'guardian_name', 'guardian_phone',
                'emergency_contact', 'status', 'notes', 'photo_path'
            ]
            
            data = {}
            for key, value in kwargs.items():
                if key in allowed_fields:
                    data[key] = value
                    setattr(self, key, value)
            
            if data:
                data['updated_at'] = datetime.now()
                rows_affected = db_handler.update('students', data, 'id = ?', (self.id,))
                
                if rows_affected > 0:
                    self.updated_at = datetime.now()
                    self.logger.info(f"تم تحديث بيانات الطالب: {self.full_name}")
                    return True
            
            return False
        except Exception as e:
            self.logger.error(f"خطأ في تحديث بيانات الطالب: {e}")
            return False
    
    def delete(self) -> bool:
        """حذف الطالب"""
        try:
            rows_affected = db_handler.delete('students', 'id = ?', (self.id,))
            
            if rows_affected > 0:
                self.logger.info(f"تم حذف الطالب: {self.full_name}")
                return True
            
            return False
        except Exception as e:
            self.logger.error(f"خطأ في حذف الطالب: {e}")
            return False
    
    def get_groups(self) -> List[Dict]:
        """الحصول على مجموعات الطالب"""
        query = """
        SELECT g.*, c.name as course_name, sg.enrollment_date, sg.status as enrollment_status
        FROM groups g
        JOIN student_groups sg ON g.id = sg.group_id
        JOIN courses c ON g.course_id = c.id
        WHERE sg.student_id = ?
        ORDER BY sg.enrollment_date DESC
        """
        
        rows = db_handler.fetch_all(query, (self.id,))
        return [dict(row) for row in rows]
    
    def get_payments(self) -> List[Dict]:
        """الحصول على مدفوعات الطالب"""
        query = """
        SELECT p.*, g.name as group_name, c.name as course_name
        FROM payments p
        LEFT JOIN groups g ON p.group_id = g.id
        LEFT JOIN courses c ON g.course_id = c.id
        WHERE p.student_id = ?
        ORDER BY p.payment_date DESC
        """
        
        rows = db_handler.fetch_all(query, (self.id,))
        return [dict(row) for row in rows]
    
    def get_attendance_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص الحضور"""
        query = """
        SELECT 
            COUNT(*) as total_sessions,
            SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,
            SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,
            SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count,
            SUM(CASE WHEN status = 'excused' THEN 1 ELSE 0 END) as excused_count
        FROM attendance
        WHERE student_id = ?
        """
        
        row = db_handler.fetch_one(query, (self.id,))
        
        if row:
            total = row['total_sessions'] or 0
            present = row['present_count'] or 0
            
            return {
                'total_sessions': total,
                'present_count': present,
                'absent_count': row['absent_count'] or 0,
                'late_count': row['late_count'] or 0,
                'excused_count': row['excused_count'] or 0,
                'attendance_rate': (present / total * 100) if total > 0 else 0
            }
        
        return {
            'total_sessions': 0,
            'present_count': 0,
            'absent_count': 0,
            'late_count': 0,
            'excused_count': 0,
            'attendance_rate': 0
        }
    
    @classmethod
    def _from_row(cls, row) -> 'Student':
        """إنشاء كائن طالب من سجل قاعدة البيانات"""
        return cls(
            student_id=row['id'],
            user_id=row['user_id'],
            student_code=row['student_code'],
            first_name=row['first_name'],
            last_name=row['last_name'],
            date_of_birth=date.fromisoformat(row['date_of_birth']) if row['date_of_birth'] else None,
            gender=row['gender'],
            phone=row['phone'],
            email=row['email'],
            address=row['address'],
            guardian_name=row['guardian_name'],
            guardian_phone=row['guardian_phone'],
            emergency_contact=row['emergency_contact'],
            enrollment_date=date.fromisoformat(row['enrollment_date']) if row['enrollment_date'] else None,
            status=row['status'],
            notes=row['notes'],
            photo_path=row['photo_path'],
            created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
            updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل الطالب إلى قاموس"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'student_code': self.student_code,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'full_name': self.full_name,
            'date_of_birth': self.date_of_birth.isoformat() if self.date_of_birth else None,
            'age': self.age,
            'gender': self.gender,
            'gender_name': self.GENDERS.get(self.gender, self.gender),
            'phone': self.phone,
            'email': self.email,
            'address': self.address,
            'guardian_name': self.guardian_name,
            'guardian_phone': self.guardian_phone,
            'emergency_contact': self.emergency_contact,
            'enrollment_date': self.enrollment_date.isoformat() if self.enrollment_date else None,
            'status': self.status,
            'status_name': self.STATUSES.get(self.status, self.status),
            'notes': self.notes,
            'photo_path': self.photo_path,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __str__(self):
        return f"Student(id={self.id}, code='{self.student_code}', name='{self.full_name}')"
    
    def __repr__(self):
        return self.__str__()
