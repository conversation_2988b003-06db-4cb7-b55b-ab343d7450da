"""
نموذج الكورس - Course Model
"""

import logging
from datetime import datetime
from typing import Optional, Dict, Any, List
from decimal import Decimal
from database.db_handler import db_handler

class Course:
    """نموذج الكورس"""
    
    # مستويات الكورس المتاحة
    LEVELS = {
        'beginner': 'مبتدئ',
        'intermediate': 'متوسط',
        'advanced': 'متقدم'
    }
    
    def __init__(self, course_id: int = None, course_code: str = None,
                 name: str = None, description: str = None,
                 duration_hours: int = None, price: Decimal = None,
                 max_students: int = 20, category: str = None,
                 level: str = 'beginner', is_active: bool = True,
                 created_at: datetime = None, updated_at: datetime = None):
        """
        تهيئة نموذج الكورس
        """
        self.id = course_id
        self.course_code = course_code
        self.name = name
        self.description = description
        self.duration_hours = duration_hours
        self.price = price
        self.max_students = max_students
        self.category = category
        self.level = level
        self.is_active = is_active
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()
        self.logger = logging.getLogger(__name__)
    
    @classmethod
    def create(cls, name: str, course_code: str = None, **kwargs) -> 'Course':
        """
        إنشاء كورس جديد
        
        Args:
            name: اسم الكورس
            course_code: رمز الكورس
            **kwargs: باقي البيانات
            
        Returns:
            Course: الكورس الجديد
        """
        # التحقق من صحة البيانات
        if not name:
            raise ValueError("اسم الكورس مطلوب")
        
        # إنشاء رمز الكورس إذا لم يتم تمريره
        if not course_code:
            course_code = cls._generate_course_code()
        
        # التحقق من عدم وجود رمز الكورس مسبقاً
        if cls.get_by_code(course_code):
            raise ValueError("رمز الكورس موجود مسبقاً")
        
        # إعداد البيانات
        course_data = {
            'course_code': course_code,
            'name': name,
            'max_students': 20,
            'level': 'beginner',
            'is_active': True,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
        
        # إضافة البيانات الإضافية
        allowed_fields = [
            'description', 'duration_hours', 'price', 'max_students',
            'category', 'level', 'is_active'
        ]
        
        for field in allowed_fields:
            if field in kwargs and kwargs[field] is not None:
                course_data[field] = kwargs[field]
        
        # إدراج في قاعدة البيانات
        course_id = db_handler.insert('courses', course_data)
        
        # إنشاء كائن الكورس
        course = cls(
            course_id=course_id,
            course_code=course_code,
            name=name,
            **kwargs
        )
        
        course.logger.info(f"تم إنشاء كورس جديد: {name} ({course_code})")
        return course
    
    @classmethod
    def get_by_id(cls, course_id: int) -> Optional['Course']:
        """الحصول على كورس بالمعرف"""
        query = "SELECT * FROM courses WHERE id = ?"
        row = db_handler.fetch_one(query, (course_id,))
        
        if row:
            return cls._from_row(row)
        return None
    
    @classmethod
    def get_by_code(cls, course_code: str) -> Optional['Course']:
        """الحصول على كورس بالرمز"""
        query = "SELECT * FROM courses WHERE course_code = ?"
        row = db_handler.fetch_one(query, (course_code,))
        
        if row:
            return cls._from_row(row)
        return None
    
    @classmethod
    def search(cls, search_term: str = None, category: str = None,
               level: str = None, is_active: bool = None,
               limit: int = None) -> List['Course']:
        """
        البحث عن الكورسات
        
        Args:
            search_term: مصطلح البحث (اسم أو رمز)
            category: الفئة
            level: المستوى
            is_active: حالة النشاط
            limit: عدد النتائج المحدود
            
        Returns:
            List[Course]: قائمة الكورسات
        """
        query = "SELECT * FROM courses WHERE 1=1"
        params = []
        
        if search_term:
            query += " AND (name LIKE ? OR course_code LIKE ? OR description LIKE ?)"
            search_pattern = f"%{search_term}%"
            params.extend([search_pattern, search_pattern, search_pattern])
        
        if category:
            query += " AND category LIKE ?"
            params.append(f"%{category}%")
        
        if level:
            query += " AND level = ?"
            params.append(level)
        
        if is_active is not None:
            query += " AND is_active = ?"
            params.append(is_active)
        
        query += " ORDER BY name"
        
        if limit:
            query += f" LIMIT {limit}"
        
        rows = db_handler.fetch_all(query, tuple(params) if params else None)
        return [cls._from_row(row) for row in rows]
    
    @classmethod
    def get_all(cls, is_active: bool = None) -> List['Course']:
        """الحصول على جميع الكورسات"""
        return cls.search(is_active=is_active)
    
    @classmethod
    def get_categories(cls) -> List[str]:
        """الحصول على جميع الفئات المتاحة"""
        query = "SELECT DISTINCT category FROM courses WHERE category IS NOT NULL ORDER BY category"
        rows = db_handler.fetch_all(query)
        return [row['category'] for row in rows]
    
    @classmethod
    def _generate_course_code(cls) -> str:
        """إنشاء رمز كورس جديد"""
        # الحصول على آخر رمز كورس
        query = "SELECT course_code FROM courses ORDER BY id DESC LIMIT 1"
        row = db_handler.fetch_one(query)
        
        if row and row['course_code']:
            # استخراج الرقم من آخر رمز
            last_code = row['course_code']
            if last_code.startswith('CRS'):
                try:
                    last_number = int(last_code[3:])
                    new_number = last_number + 1
                except ValueError:
                    new_number = 1
            else:
                new_number = 1
        else:
            new_number = 1
        
        return f"CRS{new_number:04d}"  # CRS0001, CRS0002, etc.
    
    def update(self, **kwargs) -> bool:
        """تحديث بيانات الكورس"""
        try:
            # الحقول المسموح بتحديثها
            allowed_fields = [
                'name', 'description', 'duration_hours', 'price',
                'max_students', 'category', 'level', 'is_active'
            ]
            
            data = {}
            for key, value in kwargs.items():
                if key in allowed_fields:
                    data[key] = value
                    setattr(self, key, value)
            
            if data:
                data['updated_at'] = datetime.now()
                rows_affected = db_handler.update('courses', data, 'id = ?', (self.id,))
                
                if rows_affected > 0:
                    self.updated_at = datetime.now()
                    self.logger.info(f"تم تحديث بيانات الكورس: {self.name}")
                    return True
            
            return False
        except Exception as e:
            self.logger.error(f"خطأ في تحديث بيانات الكورس: {e}")
            return False
    
    def delete(self) -> bool:
        """حذف الكورس"""
        try:
            # التحقق من وجود مجموعات مرتبطة
            query = "SELECT COUNT(*) as count FROM groups WHERE course_id = ?"
            row = db_handler.fetch_one(query, (self.id,))
            
            if row and row['count'] > 0:
                raise ValueError("لا يمكن حذف الكورس لوجود مجموعات مرتبطة به")
            
            rows_affected = db_handler.delete('courses', 'id = ?', (self.id,))
            
            if rows_affected > 0:
                self.logger.info(f"تم حذف الكورس: {self.name}")
                return True
            
            return False
        except Exception as e:
            self.logger.error(f"خطأ في حذف الكورس: {e}")
            return False
    
    def get_groups(self) -> List[Dict]:
        """الحصول على مجموعات الكورس"""
        query = """
        SELECT g.*, t.first_name || ' ' || t.last_name as teacher_name,
               COUNT(sg.student_id) as enrolled_students
        FROM groups g
        LEFT JOIN teachers t ON g.teacher_id = t.id
        LEFT JOIN student_groups sg ON g.id = sg.group_id AND sg.status = 'active'
        WHERE g.course_id = ?
        GROUP BY g.id
        ORDER BY g.start_date DESC
        """
        
        rows = db_handler.fetch_all(query, (self.id,))
        return [dict(row) for row in rows]
    
    def get_active_groups(self) -> List[Dict]:
        """الحصول على المجموعات النشطة للكورس"""
        query = """
        SELECT g.*, t.first_name || ' ' || t.last_name as teacher_name,
               COUNT(sg.student_id) as enrolled_students
        FROM groups g
        LEFT JOIN teachers t ON g.teacher_id = t.id
        LEFT JOIN student_groups sg ON g.id = sg.group_id AND sg.status = 'active'
        WHERE g.course_id = ? AND g.status = 'active'
        GROUP BY g.id
        ORDER BY g.start_date DESC
        """
        
        rows = db_handler.fetch_all(query, (self.id,))
        return [dict(row) for row in rows]
    
    def get_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الكورس"""
        # عدد المجموعات
        query = "SELECT COUNT(*) as total_groups FROM groups WHERE course_id = ?"
        row = db_handler.fetch_one(query, (self.id,))
        total_groups = row['total_groups'] if row else 0
        
        # عدد المجموعات النشطة
        query = "SELECT COUNT(*) as active_groups FROM groups WHERE course_id = ? AND status = 'active'"
        row = db_handler.fetch_one(query, (self.id,))
        active_groups = row['active_groups'] if row else 0
        
        # عدد الطلاب المسجلين
        query = """
        SELECT COUNT(DISTINCT sg.student_id) as total_students
        FROM groups g
        JOIN student_groups sg ON g.id = sg.group_id
        WHERE g.course_id = ? AND sg.status = 'active'
        """
        row = db_handler.fetch_one(query, (self.id,))
        total_students = row['total_students'] if row else 0
        
        # إجمالي الإيرادات
        query = """
        SELECT SUM(p.amount) as total_revenue
        FROM payments p
        JOIN groups g ON p.group_id = g.id
        WHERE g.course_id = ? AND p.status = 'paid'
        """
        row = db_handler.fetch_one(query, (self.id,))
        total_revenue = float(row['total_revenue']) if row and row['total_revenue'] else 0
        
        return {
            'total_groups': total_groups,
            'active_groups': active_groups,
            'total_students': total_students,
            'total_revenue': total_revenue,
            'avg_students_per_group': round(total_students / total_groups, 1) if total_groups > 0 else 0
        }
    
    def get_teachers(self) -> List[Dict]:
        """الحصول على المعلمين الذين يدرسون هذا الكورس"""
        query = """
        SELECT DISTINCT t.*, COUNT(g.id) as groups_count
        FROM teachers t
        JOIN groups g ON t.id = g.teacher_id
        WHERE g.course_id = ?
        GROUP BY t.id
        ORDER BY t.first_name, t.last_name
        """
        
        rows = db_handler.fetch_all(query, (self.id,))
        return [dict(row) for row in rows]
    
    @classmethod
    def _from_row(cls, row) -> 'Course':
        """إنشاء كائن كورس من سجل قاعدة البيانات"""
        return cls(
            course_id=row['id'],
            course_code=row['course_code'],
            name=row['name'],
            description=row['description'],
            duration_hours=row['duration_hours'],
            price=Decimal(str(row['price'])) if row['price'] else None,
            max_students=row['max_students'],
            category=row['category'],
            level=row['level'],
            is_active=bool(row['is_active']),
            created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
            updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل الكورس إلى قاموس"""
        return {
            'id': self.id,
            'course_code': self.course_code,
            'name': self.name,
            'description': self.description,
            'duration_hours': self.duration_hours,
            'price': float(self.price) if self.price else None,
            'max_students': self.max_students,
            'category': self.category,
            'level': self.level,
            'level_name': self.LEVELS.get(self.level, self.level),
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __str__(self):
        return f"Course(id={self.id}, code='{self.course_code}', name='{self.name}')"
    
    def __repr__(self):
        return self.__str__()
