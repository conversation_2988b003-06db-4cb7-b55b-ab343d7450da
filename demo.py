#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض توضيحي للنظام - System Demo
عرض توضيحي لوظائف النظام بدون واجهة رسومية
"""

import sys
import os
from pathlib import Path
from datetime import date, datetime
from decimal import Decimal

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def demo_database():
    """عرض توضيحي لقاعدة البيانات"""
    print("🗄️ عرض توضيحي لقاعدة البيانات")
    print("-" * 40)
    
    try:
        from database.db_handler import db_handler
        
        # تهيئة قاعدة البيانات
        db_handler.initialize_database()
        print("✅ تم تهيئة قاعدة البيانات")
        
        # عرض الجداول
        tables = db_handler.get_all_tables()
        print(f"📊 عدد الجداول: {len(tables)}")
        for table in tables:
            print(f"  - {table}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def demo_users():
    """عرض توضيحي للمستخدمين"""
    print("\n👥 عرض توضيحي للمستخدمين")
    print("-" * 40)
    
    try:
        from models.user import User
        
        # الحصول على جميع المستخدمين
        users = User.get_all()
        print(f"👤 عدد المستخدمين: {len(users)}")
        
        for user in users:
            print(f"  - {user.username} ({user.full_name}) - {User.ROLES.get(user.role, user.role)}")
        
        # اختبار تسجيل الدخول
        print("\n🔐 اختبار تسجيل الدخول...")
        admin_user = User.authenticate('admin', 'admin123')
        if admin_user:
            print(f"✅ نجح تسجيل الدخول: {admin_user.full_name}")
        else:
            print("❌ فشل تسجيل الدخول")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def demo_students():
    """عرض توضيحي للطلاب"""
    print("\n🎓 عرض توضيحي للطلاب")
    print("-" * 40)
    
    try:
        from models.student import Student
        
        # إنشاء طلاب تجريبيين
        students_data = [
            {
                'first_name': 'أحمد',
                'last_name': 'محمد',
                'email': '<EMAIL>',
                'phone': '0501234567',
                'date_of_birth': date(2000, 5, 15),
                'gender': 'male'
            },
            {
                'first_name': 'فاطمة',
                'last_name': 'علي',
                'email': '<EMAIL>',
                'phone': '0507654321',
                'date_of_birth': date(1999, 8, 20),
                'gender': 'female'
            }
        ]
        
        created_students = []
        for student_data in students_data:
            try:
                student = Student.create(**student_data)
                created_students.append(student)
                print(f"✅ تم إنشاء الطالب: {student.full_name} ({student.student_code})")
            except ValueError as e:
                # الطالب موجود مسبقاً
                existing = Student.get_by_code(Student._generate_student_code())
                if existing:
                    print(f"ℹ️ الطالب موجود: {existing.full_name}")
        
        # عرض جميع الطلاب
        all_students = Student.get_all()
        print(f"\n📊 إجمالي الطلاب: {len(all_students)}")
        
        for student in all_students[:5]:  # عرض أول 5 طلاب
            print(f"  - {student.student_code}: {student.full_name} ({student.age} سنة)")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def demo_teachers():
    """عرض توضيحي للمعلمين"""
    print("\n👨‍🏫 عرض توضيحي للمعلمين")
    print("-" * 40)
    
    try:
        from models.teacher import Teacher
        
        # إنشاء معلمين تجريبيين
        teachers_data = [
            {
                'first_name': 'محمد',
                'last_name': 'أحمد',
                'email': '<EMAIL>',
                'phone': '0501111111',
                'specialization': 'الرياضيات',
                'qualification': 'ماجستير رياضيات',
                'salary': Decimal('8000.00')
            },
            {
                'first_name': 'سارة',
                'last_name': 'خالد',
                'email': '<EMAIL>',
                'phone': '0502222222',
                'specialization': 'اللغة العربية',
                'qualification': 'بكالوريوس أدب عربي',
                'salary': Decimal('7500.00')
            }
        ]
        
        for teacher_data in teachers_data:
            try:
                teacher = Teacher.create(**teacher_data)
                print(f"✅ تم إنشاء المعلم: {teacher.full_name} ({teacher.teacher_code})")
            except ValueError:
                print(f"ℹ️ المعلم موجود مسبقاً")
        
        # عرض جميع المعلمين
        all_teachers = Teacher.get_all()
        print(f"\n📊 إجمالي المعلمين: {len(all_teachers)}")
        
        for teacher in all_teachers:
            print(f"  - {teacher.teacher_code}: {teacher.full_name} - {teacher.specialization}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def demo_courses():
    """عرض توضيحي للكورسات"""
    print("\n📚 عرض توضيحي للكورسات")
    print("-" * 40)
    
    try:
        from models.course import Course
        
        # إنشاء كورسات تجريبية
        courses_data = [
            {
                'name': 'أساسيات الرياضيات',
                'description': 'كورس تأسيسي في الرياضيات',
                'duration_hours': 40,
                'price': Decimal('500.00'),
                'category': 'رياضيات',
                'level': 'beginner'
            },
            {
                'name': 'اللغة العربية المتقدمة',
                'description': 'كورس متقدم في اللغة العربية',
                'duration_hours': 60,
                'price': Decimal('750.00'),
                'category': 'لغة عربية',
                'level': 'advanced'
            }
        ]
        
        for course_data in courses_data:
            try:
                course = Course.create(**course_data)
                print(f"✅ تم إنشاء الكورس: {course.name} ({course.course_code})")
            except ValueError:
                print(f"ℹ️ الكورس موجود مسبقاً")
        
        # عرض جميع الكورسات
        all_courses = Course.get_all()
        print(f"\n📊 إجمالي الكورسات: {len(all_courses)}")
        
        for course in all_courses:
            level_name = Course.LEVELS.get(course.level, course.level)
            print(f"  - {course.course_code}: {course.name} - {level_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def demo_payments():
    """عرض توضيحي للمدفوعات"""
    print("\n💰 عرض توضيحي للمدفوعات")
    print("-" * 40)
    
    try:
        from models.finance import Payment
        from models.student import Student
        
        # الحصول على أول طالب
        students = Student.get_all()
        if not students:
            print("⚠️ لا يوجد طلاب لإنشاء مدفوعات")
            return True
        
        student = students[0]
        
        # إنشاء دفعة تجريبية
        try:
            payment = Payment.create(
                student_id=student.id,
                amount=Decimal('500.00'),
                payment_type='tuition',
                payment_method='cash',
                payment_date=date.today()
            )
            print(f"✅ تم إنشاء دفعة: {payment.receipt_number} - {payment.amount} ريال")
        except ValueError:
            print("ℹ️ الدفعة موجودة مسبقاً")
        
        # عرض المدفوعات
        payments = Payment.search(limit=5)
        print(f"\n📊 عدد المدفوعات: {len(payments)}")
        
        for payment in payments:
            print(f"  - {payment.receipt_number}: {payment.amount} ريال - {Payment.STATUSES.get(payment.status, payment.status)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def demo_backup():
    """عرض توضيحي للنسخ الاحتياطية"""
    print("\n💾 عرض توضيحي للنسخ الاحتياطية")
    print("-" * 40)
    
    try:
        from utils.backup import BackupManager
        
        backup_manager = BackupManager()
        
        # إنشاء نسخة احتياطية
        backup_path = backup_manager.create_database_backup("demo_backup")
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
        
        # عرض النسخ الاحتياطية
        backups = backup_manager.list_backups()
        print(f"\n📊 عدد النسخ الاحتياطية: {len(backups)}")
        
        for backup in backups[:3]:  # عرض أول 3 نسخ
            print(f"  - {backup['name']} ({backup['size_mb']} MB)")
        
        # حذف النسخة التجريبية
        if os.path.exists(backup_path):
            os.remove(backup_path)
            print("🗑️ تم حذف النسخة التجريبية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def main():
    """الدالة الرئيسية للعرض التوضيحي"""
    print("🎓 عرض توضيحي لنظام إدارة الأكاديمية التعليمية")
    print("=" * 60)
    
    demos = [
        ("قاعدة البيانات", demo_database),
        ("المستخدمين", demo_users),
        ("الطلاب", demo_students),
        ("المعلمين", demo_teachers),
        ("الكورسات", demo_courses),
        ("المدفوعات", demo_payments),
        ("النسخ الاحتياطية", demo_backup),
    ]
    
    success_count = 0
    
    for demo_name, demo_func in demos:
        try:
            if demo_func():
                success_count += 1
        except Exception as e:
            print(f"❌ خطأ في عرض {demo_name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج العرض التوضيحي:")
    print(f"✅ نجح: {success_count}/{len(demos)}")
    print(f"📈 معدل النجاح: {success_count/len(demos)*100:.1f}%")
    
    if success_count == len(demos):
        print("\n🎉 جميع العروض التوضيحية نجحت! النظام يعمل بشكل صحيح.")
    else:
        print(f"\n⚠️ بعض العروض فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    print("\nيمكنك الآن تشغيل النظام الكامل باستخدام:")
    print("  python main.py")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف العرض التوضيحي")
    except Exception as e:
        print(f"\n❌ خطأ فادح: {e}")
        sys.exit(1)
