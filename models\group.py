"""
نموذج المجموعة - Group Model
"""

import logging
from datetime import datetime, date, time
from typing import Optional, Dict, Any, List
from database.db_handler import db_handler

class Group:
    """نموذج المجموعة"""
    
    # حالات المجموعة المتاحة
    STATUSES = {
        'active': 'نشطة',
        'completed': 'مكتملة',
        'cancelled': 'ملغية'
    }
    
    # أيام الأسبوع
    WEEKDAYS = {
        'sunday': 'الأحد',
        'monday': 'الاثنين',
        'tuesday': 'الثلاثاء',
        'wednesday': 'الأربعاء',
        'thursday': 'الخميس',
        'friday': 'الجمعة',
        'saturday': 'السبت'
    }
    
    def __init__(self, group_id: int = None, group_code: str = None,
                 name: str = None, course_id: int = None, teacher_id: int = None,
                 start_date: date = None, end_date: date = None,
                 schedule_days: str = None, start_time: time = None,
                 end_time: time = None, max_students: int = 15,
                 current_students: int = 0, status: str = 'active',
                 classroom: str = None, notes: str = None,
                 created_at: datetime = None, updated_at: datetime = None):
        """
        تهيئة نموذج المجموعة
        """
        self.id = group_id
        self.group_code = group_code
        self.name = name
        self.course_id = course_id
        self.teacher_id = teacher_id
        self.start_date = start_date
        self.end_date = end_date
        self.schedule_days = schedule_days
        self.start_time = start_time
        self.end_time = end_time
        self.max_students = max_students
        self.current_students = current_students
        self.status = status
        self.classroom = classroom
        self.notes = notes
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()
        self.logger = logging.getLogger(__name__)
    
    @property
    def is_full(self) -> bool:
        """هل المجموعة ممتلئة"""
        return self.current_students >= self.max_students
    
    @property
    def available_spots(self) -> int:
        """عدد الأماكن المتاحة"""
        return max(0, self.max_students - self.current_students)
    
    @property
    def schedule_days_list(self) -> List[str]:
        """قائمة أيام الجدولة"""
        if self.schedule_days:
            return [day.strip() for day in self.schedule_days.split(',')]
        return []
    
    @classmethod
    def create(cls, name: str, course_id: int, group_code: str = None,
               **kwargs) -> 'Group':
        """
        إنشاء مجموعة جديدة
        
        Args:
            name: اسم المجموعة
            course_id: معرف الكورس
            group_code: رمز المجموعة
            **kwargs: باقي البيانات
            
        Returns:
            Group: المجموعة الجديدة
        """
        # التحقق من صحة البيانات
        if not name or not course_id:
            raise ValueError("اسم المجموعة ومعرف الكورس مطلوبان")
        
        # التحقق من وجود الكورس
        from .course import Course
        course = Course.get_by_id(course_id)
        if not course:
            raise ValueError("الكورس غير موجود")
        
        # إنشاء رمز المجموعة إذا لم يتم تمريره
        if not group_code:
            group_code = cls._generate_group_code()
        
        # التحقق من عدم وجود رمز المجموعة مسبقاً
        if cls.get_by_code(group_code):
            raise ValueError("رمز المجموعة موجود مسبقاً")
        
        # إعداد البيانات
        group_data = {
            'group_code': group_code,
            'name': name,
            'course_id': course_id,
            'max_students': 15,
            'current_students': 0,
            'status': 'active',
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
        
        # إضافة البيانات الإضافية
        allowed_fields = [
            'teacher_id', 'start_date', 'end_date', 'schedule_days',
            'start_time', 'end_time', 'max_students', 'classroom', 'notes'
        ]
        
        for field in allowed_fields:
            if field in kwargs and kwargs[field] is not None:
                group_data[field] = kwargs[field]
        
        # إدراج في قاعدة البيانات
        group_id = db_handler.insert('groups', group_data)
        
        # إنشاء كائن المجموعة
        group = cls(
            group_id=group_id,
            group_code=group_code,
            name=name,
            course_id=course_id,
            **kwargs
        )
        
        group.logger.info(f"تم إنشاء مجموعة جديدة: {name} ({group_code})")
        return group
    
    @classmethod
    def get_by_id(cls, group_id: int) -> Optional['Group']:
        """الحصول على مجموعة بالمعرف"""
        query = "SELECT * FROM groups WHERE id = ?"
        row = db_handler.fetch_one(query, (group_id,))
        
        if row:
            return cls._from_row(row)
        return None
    
    @classmethod
    def get_by_code(cls, group_code: str) -> Optional['Group']:
        """الحصول على مجموعة بالرمز"""
        query = "SELECT * FROM groups WHERE group_code = ?"
        row = db_handler.fetch_one(query, (group_code,))
        
        if row:
            return cls._from_row(row)
        return None
    
    @classmethod
    def search(cls, search_term: str = None, course_id: int = None,
               teacher_id: int = None, status: str = None,
               limit: int = None) -> List['Group']:
        """
        البحث عن المجموعات
        
        Args:
            search_term: مصطلح البحث (اسم أو رمز)
            course_id: معرف الكورس
            teacher_id: معرف المعلم
            status: الحالة
            limit: عدد النتائج المحدود
            
        Returns:
            List[Group]: قائمة المجموعات
        """
        query = "SELECT * FROM groups WHERE 1=1"
        params = []
        
        if search_term:
            query += " AND (name LIKE ? OR group_code LIKE ?)"
            search_pattern = f"%{search_term}%"
            params.extend([search_pattern, search_pattern])
        
        if course_id:
            query += " AND course_id = ?"
            params.append(course_id)
        
        if teacher_id:
            query += " AND teacher_id = ?"
            params.append(teacher_id)
        
        if status:
            query += " AND status = ?"
            params.append(status)
        
        query += " ORDER BY start_date DESC, name"
        
        if limit:
            query += f" LIMIT {limit}"
        
        rows = db_handler.fetch_all(query, tuple(params) if params else None)
        return [cls._from_row(row) for row in rows]
    
    @classmethod
    def get_all(cls, status: str = None) -> List['Group']:
        """الحصول على جميع المجموعات"""
        return cls.search(status=status)
    
    @classmethod
    def _generate_group_code(cls) -> str:
        """إنشاء رمز مجموعة جديد"""
        # الحصول على آخر رمز مجموعة
        query = "SELECT group_code FROM groups ORDER BY id DESC LIMIT 1"
        row = db_handler.fetch_one(query)
        
        if row and row['group_code']:
            # استخراج الرقم من آخر رمز
            last_code = row['group_code']
            if last_code.startswith('GRP'):
                try:
                    last_number = int(last_code[3:])
                    new_number = last_number + 1
                except ValueError:
                    new_number = 1
            else:
                new_number = 1
        else:
            new_number = 1
        
        return f"GRP{new_number:04d}"  # GRP0001, GRP0002, etc.
    
    def update(self, **kwargs) -> bool:
        """تحديث بيانات المجموعة"""
        try:
            # الحقول المسموح بتحديثها
            allowed_fields = [
                'name', 'teacher_id', 'start_date', 'end_date',
                'schedule_days', 'start_time', 'end_time', 'max_students',
                'status', 'classroom', 'notes'
            ]
            
            data = {}
            for key, value in kwargs.items():
                if key in allowed_fields:
                    data[key] = value
                    setattr(self, key, value)
            
            if data:
                data['updated_at'] = datetime.now()
                rows_affected = db_handler.update('groups', data, 'id = ?', (self.id,))
                
                if rows_affected > 0:
                    self.updated_at = datetime.now()
                    self.logger.info(f"تم تحديث بيانات المجموعة: {self.name}")
                    return True
            
            return False
        except Exception as e:
            self.logger.error(f"خطأ في تحديث بيانات المجموعة: {e}")
            return False
    
    def delete(self) -> bool:
        """حذف المجموعة"""
        try:
            # التحقق من وجود طلاب مسجلين
            query = "SELECT COUNT(*) as count FROM student_groups WHERE group_id = ?"
            row = db_handler.fetch_one(query, (self.id,))
            
            if row and row['count'] > 0:
                raise ValueError("لا يمكن حذف المجموعة لوجود طلاب مسجلين بها")
            
            rows_affected = db_handler.delete('groups', 'id = ?', (self.id,))
            
            if rows_affected > 0:
                self.logger.info(f"تم حذف المجموعة: {self.name}")
                return True
            
            return False
        except Exception as e:
            self.logger.error(f"خطأ في حذف المجموعة: {e}")
            return False
    
    def add_student(self, student_id: int) -> bool:
        """إضافة طالب للمجموعة"""
        try:
            # التحقق من وجود مكان متاح
            if self.is_full:
                raise ValueError("المجموعة ممتلئة")
            
            # التحقق من عدم تسجيل الطالب مسبقاً
            query = "SELECT id FROM student_groups WHERE student_id = ? AND group_id = ?"
            row = db_handler.fetch_one(query, (student_id, self.id))
            
            if row:
                raise ValueError("الطالب مسجل مسبقاً في هذه المجموعة")
            
            # إضافة الطالب
            enrollment_data = {
                'student_id': student_id,
                'group_id': self.id,
                'enrollment_date': date.today(),
                'status': 'active'
            }
            
            db_handler.insert('student_groups', enrollment_data)
            
            # تحديث عدد الطلاب الحالي
            self.update_student_count()
            
            self.logger.info(f"تم إضافة طالب (ID: {student_id}) للمجموعة: {self.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في إضافة طالب للمجموعة: {e}")
            return False
    
    def remove_student(self, student_id: int) -> bool:
        """إزالة طالب من المجموعة"""
        try:
            rows_affected = db_handler.delete(
                'student_groups', 
                'student_id = ? AND group_id = ?', 
                (student_id, self.id)
            )
            
            if rows_affected > 0:
                # تحديث عدد الطلاب الحالي
                self.update_student_count()
                
                self.logger.info(f"تم إزالة طالب (ID: {student_id}) من المجموعة: {self.name}")
                return True
            
            return False
        except Exception as e:
            self.logger.error(f"خطأ في إزالة طالب من المجموعة: {e}")
            return False
    
    def update_student_count(self):
        """تحديث عدد الطلاب الحالي"""
        query = "SELECT COUNT(*) as count FROM student_groups WHERE group_id = ? AND status = 'active'"
        row = db_handler.fetch_one(query, (self.id,))
        
        if row:
            new_count = row['count']
            self.update(current_students=new_count)
            self.current_students = new_count
    
    def get_students(self) -> List[Dict]:
        """الحصول على طلاب المجموعة"""
        query = """
        SELECT s.*, sg.enrollment_date, sg.status as enrollment_status
        FROM students s
        JOIN student_groups sg ON s.id = sg.student_id
        WHERE sg.group_id = ?
        ORDER BY s.first_name, s.last_name
        """
        
        rows = db_handler.fetch_all(query, (self.id,))
        return [dict(row) for row in rows]
    
    def get_active_students(self) -> List[Dict]:
        """الحصول على الطلاب النشطين في المجموعة"""
        query = """
        SELECT s.*, sg.enrollment_date
        FROM students s
        JOIN student_groups sg ON s.id = sg.student_id
        WHERE sg.group_id = ? AND sg.status = 'active'
        ORDER BY s.first_name, s.last_name
        """
        
        rows = db_handler.fetch_all(query, (self.id,))
        return [dict(row) for row in rows]
    
    def get_course_info(self) -> Optional[Dict]:
        """الحصول على معلومات الكورس"""
        query = "SELECT * FROM courses WHERE id = ?"
        row = db_handler.fetch_one(query, (self.course_id,))
        
        if row:
            return dict(row)
        return None
    
    def get_teacher_info(self) -> Optional[Dict]:
        """الحصول على معلومات المعلم"""
        if not self.teacher_id:
            return None
        
        query = "SELECT * FROM teachers WHERE id = ?"
        row = db_handler.fetch_one(query, (self.teacher_id,))
        
        if row:
            return dict(row)
        return None
    
    @classmethod
    def _from_row(cls, row) -> 'Group':
        """إنشاء كائن مجموعة من سجل قاعدة البيانات"""
        return cls(
            group_id=row['id'],
            group_code=row['group_code'],
            name=row['name'],
            course_id=row['course_id'],
            teacher_id=row['teacher_id'],
            start_date=date.fromisoformat(row['start_date']) if row['start_date'] else None,
            end_date=date.fromisoformat(row['end_date']) if row['end_date'] else None,
            schedule_days=row['schedule_days'],
            start_time=time.fromisoformat(row['start_time']) if row['start_time'] else None,
            end_time=time.fromisoformat(row['end_time']) if row['end_time'] else None,
            max_students=row['max_students'],
            current_students=row['current_students'],
            status=row['status'],
            classroom=row['classroom'],
            notes=row['notes'],
            created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
            updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل المجموعة إلى قاموس"""
        return {
            'id': self.id,
            'group_code': self.group_code,
            'name': self.name,
            'course_id': self.course_id,
            'teacher_id': self.teacher_id,
            'start_date': self.start_date.isoformat() if self.start_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'schedule_days': self.schedule_days,
            'schedule_days_list': self.schedule_days_list,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'max_students': self.max_students,
            'current_students': self.current_students,
            'available_spots': self.available_spots,
            'is_full': self.is_full,
            'status': self.status,
            'status_name': self.STATUSES.get(self.status, self.status),
            'classroom': self.classroom,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __str__(self):
        return f"Group(id={self.id}, code='{self.group_code}', name='{self.name}')"
    
    def __repr__(self):
        return self.__str__()
