"""
شاشة تسجيل الدخول - Login Screen
"""

import logging
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QPushButton, QFrame, QMessageBox, QCheckBox, QProgressBar
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QThread, pyqtSlot
from PyQt6.QtGui import QFont, QPixmap, QIcon

from models.user import User
from utils.logger import activity_logger

class LoginWorker(QThread):
    """خيط العمل لتسجيل الدخول"""
    
    login_result = pyqtSignal(object, str)  # (user, error_message)
    
    def __init__(self, username: str, password: str):
        super().__init__()
        self.username = username
        self.password = password
    
    def run(self):
        """تنفيذ عملية تسجيل الدخول"""
        try:
            user = User.authenticate(self.username, self.password)
            if user:
                self.login_result.emit(user, "")
            else:
                self.login_result.emit(None, "اسم المستخدم أو كلمة المرور غير صحيحة")
        except Exception as e:
            self.login_result.emit(None, f"خطأ في تسجيل الدخول: {str(e)}")

class LoginScreen(QWidget):
    """شاشة تسجيل الدخول"""
    
    login_successful = pyqtSignal(object)  # إشارة نجاح تسجيل الدخول
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self.login_worker = None
        self.failed_attempts = 0
        self.max_attempts = 3
        self.lockout_timer = QTimer()
        self.lockout_timer.timeout.connect(self.unlock_login)
        
        self.init_ui()
        self.setup_styles()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("تسجيل الدخول - نظام إدارة الأكاديمية التعليمية")
        self.setFixedSize(400, 500)
        self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.WindowCloseButtonHint)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(40, 40, 40, 40)
        
        # إطار تسجيل الدخول
        login_frame = QFrame()
        login_frame.setFrameStyle(QFrame.Shape.Box)
        login_frame.setLineWidth(1)
        
        frame_layout = QVBoxLayout(login_frame)
        frame_layout.setSpacing(20)
        frame_layout.setContentsMargins(30, 30, 30, 30)
        
        # الشعار والعنوان
        self.setup_header(frame_layout)
        
        # حقول تسجيل الدخول
        self.setup_login_fields(frame_layout)
        
        # أزرار التحكم
        self.setup_buttons(frame_layout)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        frame_layout.addWidget(self.progress_bar)
        
        # رسالة الحالة
        self.status_label = QLabel()
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setVisible(False)
        frame_layout.addWidget(self.status_label)
        
        main_layout.addWidget(login_frame)
        self.setLayout(main_layout)
        
        # تركيز على حقل اسم المستخدم
        self.username_input.setFocus()
    
    def setup_header(self, layout):
        """إعداد رأس الشاشة"""
        # العنوان الرئيسي
        title_label = QLabel("نظام إدارة الأكاديمية التعليمية")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        
        # العنوان الفرعي
        subtitle_label = QLabel("تسجيل الدخول")
        subtitle_font = QFont()
        subtitle_font.setPointSize(12)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setStyleSheet("color: #7f8c8d; margin-bottom: 20px;")
        
        layout.addWidget(title_label)
        layout.addWidget(subtitle_label)
    
    def setup_login_fields(self, layout):
        """إعداد حقول تسجيل الدخول"""
        # حقل اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        username_label.setStyleSheet("font-weight: bold; color: #34495e;")
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.returnPressed.connect(self.on_login_clicked)
        
        # حقل كلمة المرور
        password_label = QLabel("كلمة المرور:")
        password_label.setStyleSheet("font-weight: bold; color: #34495e;")
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.returnPressed.connect(self.on_login_clicked)
        
        # خيار تذكر المستخدم
        self.remember_checkbox = QCheckBox("تذكرني")
        self.remember_checkbox.setStyleSheet("color: #7f8c8d;")
        
        layout.addWidget(username_label)
        layout.addWidget(self.username_input)
        layout.addWidget(password_label)
        layout.addWidget(self.password_input)
        layout.addWidget(self.remember_checkbox)
    
    def setup_buttons(self, layout):
        """إعداد الأزرار"""
        buttons_layout = QHBoxLayout()
        
        # زر تسجيل الدخول
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.clicked.connect(self.on_login_clicked)
        self.login_button.setDefault(True)
        
        # زر الإلغاء
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.clicked.connect(self.close)
        
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.login_button)
        
        layout.addLayout(buttons_layout)
    
    def setup_styles(self):
        """إعداد الأنماط"""
        self.setStyleSheet("""
            QWidget {
                background-color: #ecf0f1;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            
            QFrame {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }
            
            QLineEdit {
                padding: 10px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12px;
                background-color: white;
            }
            
            QLineEdit:focus {
                border-color: #3498db;
            }
            
            QPushButton {
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
                min-width: 100px;
            }
            
            QPushButton#login_button {
                background-color: #3498db;
                color: white;
            }
            
            QPushButton#login_button:hover {
                background-color: #2980b9;
            }
            
            QPushButton#login_button:pressed {
                background-color: #21618c;
            }
            
            QPushButton#login_button:disabled {
                background-color: #95a5a6;
            }
            
            QPushButton#cancel_button {
                background-color: #95a5a6;
                color: white;
            }
            
            QPushButton#cancel_button:hover {
                background-color: #7f8c8d;
            }
            
            QCheckBox {
                spacing: 5px;
            }
            
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            
            QCheckBox::indicator:unchecked {
                border: 2px solid #bdc3c7;
                border-radius: 3px;
                background-color: white;
            }
            
            QCheckBox::indicator:checked {
                border: 2px solid #3498db;
                border-radius: 3px;
                background-color: #3498db;
                image: url(resources/images/check.png);
            }
            
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
                background-color: #ecf0f1;
            }
            
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 3px;
            }
        """)
        
        # تطبيق معرفات الأنماط
        self.login_button.setObjectName("login_button")
        self.cancel_button.setObjectName("cancel_button")
    
    def on_login_clicked(self):
        """معالج النقر على زر تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        # التحقق من صحة البيانات
        if not username:
            self.show_error("يرجى إدخال اسم المستخدم")
            self.username_input.setFocus()
            return
        
        if not password:
            self.show_error("يرجى إدخال كلمة المرور")
            self.password_input.setFocus()
            return
        
        # بدء عملية تسجيل الدخول
        self.start_login(username, password)
    
    def start_login(self, username: str, password: str):
        """بدء عملية تسجيل الدخول"""
        # تعطيل الواجهة
        self.set_ui_enabled(False)
        self.show_progress("جاري تسجيل الدخول...")
        
        # إنشاء وتشغيل خيط العمل
        self.login_worker = LoginWorker(username, password)
        self.login_worker.login_result.connect(self.on_login_result)
        self.login_worker.start()
    
    @pyqtSlot(object, str)
    def on_login_result(self, user, error_message):
        """معالج نتيجة تسجيل الدخول"""
        # إخفاء شريط التقدم
        self.hide_progress()
        
        if user:
            # نجح تسجيل الدخول
            self.failed_attempts = 0
            
            # تسجيل النشاط
            activity_logger.log_login(user.id, user.username, True)
            
            # إرسال إشارة النجاح
            self.login_successful.emit(user)
            
            self.logger.info(f"نجح تسجيل دخول المستخدم: {user.username}")
            
        else:
            # فشل تسجيل الدخول
            self.failed_attempts += 1
            
            # تسجيل النشاط
            username = self.username_input.text().strip()
            activity_logger.log_login(0, username, False)
            
            if self.failed_attempts >= self.max_attempts:
                self.lock_login()
            else:
                remaining = self.max_attempts - self.failed_attempts
                error_message += f"\nالمحاولات المتبقية: {remaining}"
            
            self.show_error(error_message)
            self.password_input.clear()
            self.password_input.setFocus()
            
            self.logger.warning(f"فشل تسجيل دخول المستخدم: {username}")
        
        # تمكين الواجهة
        self.set_ui_enabled(True)
        
        # تنظيف خيط العمل
        if self.login_worker:
            self.login_worker.deleteLater()
            self.login_worker = None
    
    def lock_login(self):
        """قفل تسجيل الدخول مؤقتاً"""
        self.set_ui_enabled(False)
        self.show_error("تم قفل تسجيل الدخول لمدة 5 دقائق بسبب المحاولات الفاشلة")
        
        # بدء مؤقت إلغاء القفل (5 دقائق)
        self.lockout_timer.start(5 * 60 * 1000)  # 5 دقائق بالميلي ثانية
        
        self.logger.warning("تم قفل تسجيل الدخول بسبب المحاولات الفاشلة")
    
    def unlock_login(self):
        """إلغاء قفل تسجيل الدخول"""
        self.lockout_timer.stop()
        self.failed_attempts = 0
        self.set_ui_enabled(True)
        self.hide_status()
        
        self.logger.info("تم إلغاء قفل تسجيل الدخول")
    
    def set_ui_enabled(self, enabled: bool):
        """تمكين/تعطيل عناصر الواجهة"""
        self.username_input.setEnabled(enabled)
        self.password_input.setEnabled(enabled)
        self.login_button.setEnabled(enabled)
        self.remember_checkbox.setEnabled(enabled)
    
    def show_progress(self, message: str):
        """عرض شريط التقدم"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # شريط تقدم غير محدد
        self.show_status(message, "info")
    
    def hide_progress(self):
        """إخفاء شريط التقدم"""
        self.progress_bar.setVisible(False)
    
    def show_status(self, message: str, status_type: str = "info"):
        """عرض رسالة الحالة"""
        self.status_label.setText(message)
        self.status_label.setVisible(True)
        
        if status_type == "error":
            self.status_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
        elif status_type == "success":
            self.status_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        else:
            self.status_label.setStyleSheet("color: #3498db; font-weight: bold;")
    
    def hide_status(self):
        """إخفاء رسالة الحالة"""
        self.status_label.setVisible(False)
    
    def show_error(self, message: str):
        """عرض رسالة خطأ"""
        self.show_status(message, "error")
    
    def show_success(self, message: str):
        """عرض رسالة نجاح"""
        self.show_status(message, "success")
    
    def closeEvent(self, event):
        """معالج إغلاق النافذة"""
        # إيقاف خيط العمل إذا كان يعمل
        if self.login_worker and self.login_worker.isRunning():
            self.login_worker.terminate()
            self.login_worker.wait()
        
        # إيقاف المؤقت
        if self.lockout_timer.isActive():
            self.lockout_timer.stop()
        
        event.accept()
