"""
تحكم الحضور والغياب - Attendance Controller
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import date, datetime

from models.attendance import Attendance
from models.group import Group
from models.student import Student
from models.user import User
from utils.logger import activity_logger

class AttendanceController:
    """تحكم عمليات الحضور والغياب"""
    
    def __init__(self, current_user: User):
        """
        تهيئة تحكم الحضور
        
        Args:
            current_user: المستخدم الحالي
        """
        self.current_user = current_user
        self.logger = logging.getLogger(__name__)
    
    def record_attendance(self, student_id: int, group_id: int, status: str,
                         attendance_date: date = None, notes: str = None) -> Dict[str, Any]:
        """
        تسجيل حضور/غياب طالب
        
        Args:
            student_id: معرف الطالب
            group_id: معرف المجموعة
            status: حالة الحضور
            attendance_date: تاريخ الحضور
            notes: ملاحظات
            
        Returns:
            Dict: نتيجة العملية
        """
        try:
            # التحقق من الصلاحيات
            if not self.current_user.has_permission('manage_attendance'):
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لتسجيل الحضور'
                }
            
            # التحقق من صحة البيانات
            if status not in Attendance.STATUSES:
                return {
                    'success': False,
                    'message': f"حالة الحضور غير صحيحة. الحالات المتاحة: {list(Attendance.STATUSES.keys())}"
                }
            
            # التحقق من وجود الطالب والمجموعة
            student = Student.get_by_id(student_id)
            if not student:
                return {
                    'success': False,
                    'message': 'الطالب غير موجود'
                }
            
            group = Group.get_by_id(group_id)
            if not group:
                return {
                    'success': False,
                    'message': 'المجموعة غير موجودة'
                }
            
            # تسجيل الحضور
            attendance = Attendance.record(
                student_id=student_id,
                group_id=group_id,
                status=status,
                attendance_date=attendance_date,
                notes=notes,
                recorded_by=self.current_user.id
            )
            
            # تسجيل النشاط
            activity_logger.log_user_action(
                self.current_user.id,
                self.current_user.username,
                'تسجيل حضور',
                f"تم تسجيل حضور الطالب {student.full_name} في المجموعة {group.name} - {status}"
            )
            
            return {
                'success': True,
                'message': 'تم تسجيل الحضور بنجاح',
                'attendance': attendance.to_dict()
            }
            
        except ValueError as e:
            return {
                'success': False,
                'message': str(e)
            }
        except Exception as e:
            self.logger.error(f"خطأ في تسجيل الحضور: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def record_group_attendance(self, group_id: int, attendance_data: List[Dict],
                               attendance_date: date = None) -> Dict[str, Any]:
        """
        تسجيل حضور مجموعة كاملة
        
        Args:
            group_id: معرف المجموعة
            attendance_data: بيانات الحضور للطلاب
            attendance_date: تاريخ الحضور
            
        Returns:
            Dict: نتيجة العملية
        """
        try:
            # التحقق من الصلاحيات
            if not self.current_user.has_permission('manage_attendance'):
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لتسجيل الحضور'
                }
            
            # التحقق من وجود المجموعة
            group = Group.get_by_id(group_id)
            if not group:
                return {
                    'success': False,
                    'message': 'المجموعة غير موجودة'
                }
            
            # تسجيل الحضور للمجموعة
            success = Attendance.record_group_attendance(
                group_id=group_id,
                attendance_data=attendance_data,
                attendance_date=attendance_date,
                recorded_by=self.current_user.id
            )
            
            if success:
                # تسجيل النشاط
                activity_logger.log_user_action(
                    self.current_user.id,
                    self.current_user.username,
                    'تسجيل حضور مجموعة',
                    f"تم تسجيل حضور المجموعة {group.name} - {len(attendance_data)} طالب"
                )
                
                return {
                    'success': True,
                    'message': 'تم تسجيل حضور المجموعة بنجاح'
                }
            else:
                return {
                    'success': False,
                    'message': 'فشل في تسجيل حضور المجموعة'
                }
            
        except Exception as e:
            self.logger.error(f"خطأ في تسجيل حضور المجموعة: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def get_group_attendance_for_date(self, group_id: int, attendance_date: date) -> Dict[str, Any]:
        """
        الحصول على حضور المجموعة ليوم معين
        
        Args:
            group_id: معرف المجموعة
            attendance_date: التاريخ
            
        Returns:
            Dict: بيانات الحضور
        """
        try:
            # التحقق من الصلاحيات
            if not self.current_user.has_permission('view_attendance'):
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لعرض الحضور'
                }
            
            # التحقق من وجود المجموعة
            group = Group.get_by_id(group_id)
            if not group:
                return {
                    'success': False,
                    'message': 'المجموعة غير موجودة'
                }
            
            attendance_records = Attendance.get_group_attendance_for_date(group_id, attendance_date)
            
            return {
                'success': True,
                'attendance_records': attendance_records,
                'group_info': group.to_dict(),
                'date': attendance_date.isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على حضور المجموعة: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def get_student_attendance(self, student_id: int, group_id: int = None,
                              start_date: date = None, end_date: date = None) -> Dict[str, Any]:
        """
        الحصول على سجلات حضور طالب
        
        Args:
            student_id: معرف الطالب
            group_id: معرف المجموعة (اختياري)
            start_date: تاريخ البداية (اختياري)
            end_date: تاريخ النهاية (اختياري)
            
        Returns:
            Dict: سجلات الحضور
        """
        try:
            # التحقق من الصلاحيات
            if not self.current_user.has_permission('view_attendance'):
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لعرض الحضور'
                }
            
            # التحقق من وجود الطالب
            student = Student.get_by_id(student_id)
            if not student:
                return {
                    'success': False,
                    'message': 'الطالب غير موجود'
                }
            
            attendance_records = Attendance.get_by_student(
                student_id=student_id,
                group_id=group_id,
                start_date=start_date,
                end_date=end_date
            )
            
            return {
                'success': True,
                'attendance_records': [record.to_dict() for record in attendance_records],
                'student_info': student.to_dict()
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على حضور الطالب: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def get_group_attendance(self, group_id: int, start_date: date = None,
                            end_date: date = None) -> Dict[str, Any]:
        """
        الحصول على سجلات حضور مجموعة
        
        Args:
            group_id: معرف المجموعة
            start_date: تاريخ البداية (اختياري)
            end_date: تاريخ النهاية (اختياري)
            
        Returns:
            Dict: سجلات الحضور
        """
        try:
            # التحقق من الصلاحيات
            if not self.current_user.has_permission('view_attendance'):
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لعرض الحضور'
                }
            
            # التحقق من وجود المجموعة
            group = Group.get_by_id(group_id)
            if not group:
                return {
                    'success': False,
                    'message': 'المجموعة غير موجودة'
                }
            
            attendance_records = Attendance.get_by_group(
                group_id=group_id,
                start_date=start_date,
                end_date=end_date
            )
            
            return {
                'success': True,
                'attendance_records': [record.to_dict() for record in attendance_records],
                'group_info': group.to_dict()
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على حضور المجموعة: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def get_student_attendance_summary(self, student_id: int, group_id: int = None) -> Dict[str, Any]:
        """
        الحصول على ملخص حضور الطالب
        
        Args:
            student_id: معرف الطالب
            group_id: معرف المجموعة (اختياري)
            
        Returns:
            Dict: ملخص الحضور
        """
        try:
            # التحقق من وجود الطالب
            student = Student.get_by_id(student_id)
            if not student:
                return {
                    'success': False,
                    'message': 'الطالب غير موجود'
                }
            
            summary = Attendance.get_student_summary(student_id, group_id)
            
            return {
                'success': True,
                'summary': summary,
                'student_info': student.to_dict()
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على ملخص الحضور: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def get_group_attendance_summary(self, group_id: int, start_date: date = None,
                                    end_date: date = None) -> Dict[str, Any]:
        """
        الحصول على ملخص حضور المجموعة
        
        Args:
            group_id: معرف المجموعة
            start_date: تاريخ البداية (اختياري)
            end_date: تاريخ النهاية (اختياري)
            
        Returns:
            Dict: ملخص الحضور
        """
        try:
            # التحقق من وجود المجموعة
            group = Group.get_by_id(group_id)
            if not group:
                return {
                    'success': False,
                    'message': 'المجموعة غير موجودة'
                }
            
            summary = Attendance.get_group_summary(group_id, start_date, end_date)
            
            return {
                'success': True,
                'summary': summary,
                'group_info': group.to_dict()
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على ملخص حضور المجموعة: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def update_attendance(self, attendance_id: int, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        تحديث سجل حضور
        
        Args:
            attendance_id: معرف سجل الحضور
            update_data: البيانات المحدثة
            
        Returns:
            Dict: نتيجة العملية
        """
        try:
            # التحقق من الصلاحيات
            if not self.current_user.has_permission('manage_attendance'):
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لتعديل الحضور'
                }
            
            attendance = Attendance.get_by_id(attendance_id)
            if not attendance:
                return {
                    'success': False,
                    'message': 'سجل الحضور غير موجود'
                }
            
            # التحقق من صحة الحالة الجديدة
            if 'status' in update_data and update_data['status'] not in Attendance.STATUSES:
                return {
                    'success': False,
                    'message': f"حالة الحضور غير صحيحة. الحالات المتاحة: {list(Attendance.STATUSES.keys())}"
                }
            
            # إضافة معرف المسجل
            update_data['recorded_by'] = self.current_user.id
            
            if attendance.update(**update_data):
                # تسجيل النشاط
                activity_logger.log_user_action(
                    self.current_user.id,
                    self.current_user.username,
                    'تعديل حضور',
                    f"تم تعديل سجل الحضور رقم {attendance_id}"
                )
                
                return {
                    'success': True,
                    'message': 'تم تحديث سجل الحضور بنجاح',
                    'attendance': attendance.to_dict()
                }
            else:
                return {
                    'success': False,
                    'message': 'فشل في تحديث سجل الحضور'
                }
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديث سجل الحضور: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def delete_attendance(self, attendance_id: int) -> Dict[str, Any]:
        """
        حذف سجل حضور
        
        Args:
            attendance_id: معرف سجل الحضور
            
        Returns:
            Dict: نتيجة العملية
        """
        try:
            # التحقق من الصلاحيات (المدير فقط)
            if self.current_user.role != 'admin':
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لحذف سجلات الحضور'
                }
            
            attendance = Attendance.get_by_id(attendance_id)
            if not attendance:
                return {
                    'success': False,
                    'message': 'سجل الحضور غير موجود'
                }
            
            if attendance.delete():
                # تسجيل النشاط
                activity_logger.log_user_action(
                    self.current_user.id,
                    self.current_user.username,
                    'حذف سجل حضور',
                    f"تم حذف سجل الحضور رقم {attendance_id}"
                )
                
                return {
                    'success': True,
                    'message': 'تم حذف سجل الحضور بنجاح'
                }
            else:
                return {
                    'success': False,
                    'message': 'فشل في حذف سجل الحضور'
                }
            
        except Exception as e:
            self.logger.error(f"خطأ في حذف سجل الحضور: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
