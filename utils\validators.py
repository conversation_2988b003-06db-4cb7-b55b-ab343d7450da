"""
أدوات التحقق من صحة البيانات - Data Validators
"""

import re
import configparser
from datetime import datetime, date
from typing import Any, Dict, List, Optional, Union
from decimal import Decimal, InvalidOperation

class ConfigValidator:
    """مدقق إعدادات التطبيق"""
    
    @staticmethod
    def validate_config_file(config_path: str) -> Dict[str, Any]:
        """
        التحقق من صحة ملف الإعدادات
        
        Args:
            config_path: مسار ملف الإعدادات
            
        Returns:
            Dict: نتائج التحقق
        """
        result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        try:
            config = configparser.ConfigParser()
            config.read(config_path, encoding='utf-8')
            
            # التحقق من الأقسام المطلوبة
            required_sections = ['DATABASE', 'SECURITY', 'APPLICATION']
            for section in required_sections:
                if not config.has_section(section):
                    result['errors'].append(f"القسم المطلوب غير موجود: {section}")
                    result['valid'] = False
            
            # التحقق من إعدادات قاعدة البيانات
            if config.has_section('DATABASE'):
                db_type = config.get('DATABASE', 'type', fallback='sqlite')
                if db_type not in ['sqlite', 'postgresql', 'mysql']:
                    result['errors'].append(f"نوع قاعدة البيانات غير مدعوم: {db_type}")
                    result['valid'] = False
            
            # التحقق من إعدادات الأمان
            if config.has_section('SECURITY'):
                min_length = config.getint('SECURITY', 'password_min_length', fallback=8)
                if min_length < 6:
                    result['warnings'].append("الحد الأدنى لطول كلمة المرور أقل من 6 أحرف")
                
                timeout = config.getint('SECURITY', 'session_timeout', fallback=3600)
                if timeout < 300:
                    result['warnings'].append("مهلة الجلسة قصيرة جداً (أقل من 5 دقائق)")
            
        except Exception as e:
            result['valid'] = False
            result['errors'].append(f"خطأ في قراءة ملف الإعدادات: {e}")
        
        return result

class DataValidator:
    """مدقق البيانات العام"""
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """التحقق من صحة البريد الإلكتروني"""
        if not email:
            return True  # البريد الإلكتروني اختياري
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @staticmethod
    def validate_phone(phone: str) -> bool:
        """التحقق من صحة رقم الهاتف"""
        if not phone:
            return True  # رقم الهاتف اختياري
        
        # إزالة المسافات والرموز
        clean_phone = re.sub(r'[\s\-\(\)]', '', phone)
        
        # التحقق من الأرقام السعودية والدولية
        patterns = [
            r'^05\d{8}$',  # رقم سعودي
            r'^966\d{9}$',  # رقم سعودي دولي
            r'^\+966\d{9}$',  # رقم سعودي دولي مع +
            r'^\d{10,15}$'  # أرقام دولية عامة
        ]
        
        return any(re.match(pattern, clean_phone) for pattern in patterns)
    
    @staticmethod
    def validate_name(name: str, min_length: int = 2, max_length: int = 50) -> bool:
        """التحقق من صحة الاسم"""
        if not name or not name.strip():
            return False
        
        name = name.strip()
        
        # التحقق من الطول
        if len(name) < min_length or len(name) > max_length:
            return False
        
        # التحقق من الأحرف المسموحة (عربي، إنجليزي، مسافات)
        pattern = r'^[a-zA-Zأ-ي\s]+$'
        return bool(re.match(pattern, name))
    
    @staticmethod
    def validate_student_code(code: str) -> bool:
        """التحقق من صحة رمز الطالب"""
        if not code:
            return False
        
        # تنسيق: STU + 6 أرقام
        pattern = r'^STU\d{6}$'
        return bool(re.match(pattern, code))
    
    @staticmethod
    def validate_teacher_code(code: str) -> bool:
        """التحقق من صحة رمز المعلم"""
        if not code:
            return False
        
        # تنسيق: TCH + 4 أرقام
        pattern = r'^TCH\d{4}$'
        return bool(re.match(pattern, code))
    
    @staticmethod
    def validate_course_code(code: str) -> bool:
        """التحقق من صحة رمز الكورس"""
        if not code:
            return False
        
        # تنسيق: CRS + 4 أرقام
        pattern = r'^CRS\d{4}$'
        return bool(re.match(pattern, code))
    
    @staticmethod
    def validate_group_code(code: str) -> bool:
        """التحقق من صحة رمز المجموعة"""
        if not code:
            return False
        
        # تنسيق: GRP + 4 أرقام
        pattern = r'^GRP\d{4}$'
        return bool(re.match(pattern, code))
    
    @staticmethod
    def validate_date(date_str: str, date_format: str = '%Y-%m-%d') -> bool:
        """التحقق من صحة التاريخ"""
        if not date_str:
            return True  # التاريخ اختياري
        
        try:
            datetime.strptime(date_str, date_format)
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_date_range(start_date: Union[str, date], end_date: Union[str, date]) -> bool:
        """التحقق من صحة نطاق التاريخ"""
        try:
            if isinstance(start_date, str):
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            if isinstance(end_date, str):
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            
            return start_date <= end_date
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def validate_time(time_str: str, time_format: str = '%H:%M') -> bool:
        """التحقق من صحة الوقت"""
        if not time_str:
            return True  # الوقت اختياري
        
        try:
            datetime.strptime(time_str, time_format)
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_amount(amount: Union[str, float, Decimal], min_amount: float = 0) -> bool:
        """التحقق من صحة المبلغ"""
        try:
            if isinstance(amount, str):
                amount = Decimal(amount)
            elif isinstance(amount, float):
                amount = Decimal(str(amount))
            
            return amount >= Decimal(str(min_amount))
        except (ValueError, InvalidOperation, TypeError):
            return False
    
    @staticmethod
    def validate_percentage(value: Union[str, float], min_val: float = 0, max_val: float = 100) -> bool:
        """التحقق من صحة النسبة المئوية"""
        try:
            if isinstance(value, str):
                value = float(value)
            
            return min_val <= value <= max_val
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def validate_username(username: str) -> Dict[str, Any]:
        """
        التحقق من صحة اسم المستخدم
        
        Returns:
            Dict: نتائج التحقق مع الأخطاء
        """
        result = {'valid': True, 'errors': []}
        
        if not username:
            result['valid'] = False
            result['errors'].append("اسم المستخدم مطلوب")
            return result
        
        # التحقق من الطول
        if len(username) < 3:
            result['valid'] = False
            result['errors'].append("اسم المستخدم يجب أن يكون 3 أحرف على الأقل")
        
        if len(username) > 20:
            result['valid'] = False
            result['errors'].append("اسم المستخدم يجب أن يكون 20 حرف على الأكثر")
        
        # التحقق من الأحرف المسموحة
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            result['valid'] = False
            result['errors'].append("اسم المستخدم يجب أن يحتوي على أحرف وأرقام و _ فقط")
        
        # التحقق من البداية بحرف
        if not username[0].isalpha():
            result['valid'] = False
            result['errors'].append("اسم المستخدم يجب أن يبدأ بحرف")
        
        return result
    
    @staticmethod
    def validate_password(password: str, min_length: int = 8) -> Dict[str, Any]:
        """
        التحقق من صحة كلمة المرور
        
        Returns:
            Dict: نتائج التحقق مع الأخطاء
        """
        result = {'valid': True, 'errors': [], 'strength': 'ضعيف'}
        
        if not password:
            result['valid'] = False
            result['errors'].append("كلمة المرور مطلوبة")
            return result
        
        # التحقق من الطول
        if len(password) < min_length:
            result['valid'] = False
            result['errors'].append(f"كلمة المرور يجب أن تكون {min_length} أحرف على الأقل")
        
        # معايير القوة
        strength_score = 0
        
        if len(password) >= 8:
            strength_score += 1
        
        if re.search(r'[a-z]', password):
            strength_score += 1
        
        if re.search(r'[A-Z]', password):
            strength_score += 1
        
        if re.search(r'\d', password):
            strength_score += 1
        
        if re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            strength_score += 1
        
        # تحديد قوة كلمة المرور
        if strength_score >= 4:
            result['strength'] = 'قوي'
        elif strength_score >= 3:
            result['strength'] = 'متوسط'
        else:
            result['strength'] = 'ضعيف'
        
        return result
    
    @staticmethod
    def validate_student_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """التحقق من صحة بيانات الطالب"""
        result = {'valid': True, 'errors': []}
        
        # الحقول المطلوبة
        required_fields = ['first_name', 'last_name']
        for field in required_fields:
            if not data.get(field):
                result['valid'] = False
                result['errors'].append(f"الحقل مطلوب: {field}")
        
        # التحقق من الأسماء
        if data.get('first_name') and not DataValidator.validate_name(data['first_name']):
            result['valid'] = False
            result['errors'].append("الاسم الأول غير صحيح")
        
        if data.get('last_name') and not DataValidator.validate_name(data['last_name']):
            result['valid'] = False
            result['errors'].append("اسم العائلة غير صحيح")
        
        # التحقق من البريد الإلكتروني
        if data.get('email') and not DataValidator.validate_email(data['email']):
            result['valid'] = False
            result['errors'].append("البريد الإلكتروني غير صحيح")
        
        # التحقق من رقم الهاتف
        if data.get('phone') and not DataValidator.validate_phone(data['phone']):
            result['valid'] = False
            result['errors'].append("رقم الهاتف غير صحيح")
        
        # التحقق من تاريخ الميلاد
        if data.get('date_of_birth'):
            if not DataValidator.validate_date(str(data['date_of_birth'])):
                result['valid'] = False
                result['errors'].append("تاريخ الميلاد غير صحيح")
            else:
                # التحقق من أن التاريخ في الماضي
                birth_date = datetime.strptime(str(data['date_of_birth']), '%Y-%m-%d').date()
                if birth_date >= date.today():
                    result['valid'] = False
                    result['errors'].append("تاريخ الميلاد يجب أن يكون في الماضي")
        
        return result
    
    @staticmethod
    def validate_teacher_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """التحقق من صحة بيانات المعلم"""
        result = {'valid': True, 'errors': []}
        
        # الحقول المطلوبة
        required_fields = ['first_name', 'last_name']
        for field in required_fields:
            if not data.get(field):
                result['valid'] = False
                result['errors'].append(f"الحقل مطلوب: {field}")
        
        # التحقق من الأسماء
        if data.get('first_name') and not DataValidator.validate_name(data['first_name']):
            result['valid'] = False
            result['errors'].append("الاسم الأول غير صحيح")
        
        if data.get('last_name') and not DataValidator.validate_name(data['last_name']):
            result['valid'] = False
            result['errors'].append("اسم العائلة غير صحيح")
        
        # التحقق من البريد الإلكتروني
        if data.get('email') and not DataValidator.validate_email(data['email']):
            result['valid'] = False
            result['errors'].append("البريد الإلكتروني غير صحيح")
        
        # التحقق من رقم الهاتف
        if data.get('phone') and not DataValidator.validate_phone(data['phone']):
            result['valid'] = False
            result['errors'].append("رقم الهاتف غير صحيح")
        
        # التحقق من الراتب
        if data.get('salary') and not DataValidator.validate_amount(data['salary'], 0):
            result['valid'] = False
            result['errors'].append("الراتب غير صحيح")
        
        return result
