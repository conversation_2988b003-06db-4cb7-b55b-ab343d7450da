#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع للنظام - Quick Run Script
ملف مساعد لتشغيل النظام مع فحص المتطلبات
"""

import sys
import os
import subprocess
from pathlib import Path

def check_python_version():
    """فحص إصدار Python"""
    print("🐍 فحص إصدار Python...")
    
    if sys.version_info < (3, 8):
        print(f"❌ إصدار Python غير مدعوم: {sys.version}")
        print("يتطلب النظام Python 3.8 أو أحدث")
        return False
    
    print(f"✅ إصدار Python مناسب: {sys.version}")
    return True

def check_requirements():
    """فحص المتطلبات"""
    print("\n📦 فحص المتطلبات...")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ ملف requirements.txt غير موجود")
        return False
    
    try:
        # قراءة المتطلبات
        with open(requirements_file, 'r', encoding='utf-8') as f:
            requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]
        
        missing_packages = []
        
        for requirement in requirements:
            package_name = requirement.split('==')[0].split('>=')[0].split('<=')[0]
            try:
                __import__(package_name.replace('-', '_'))
                print(f"✅ {package_name}")
            except ImportError:
                missing_packages.append(requirement)
                print(f"❌ {package_name} - غير مثبت")
        
        if missing_packages:
            print(f"\n⚠️ المكتبات المفقودة: {len(missing_packages)}")
            print("هل تريد تثبيتها الآن؟ (y/n): ", end="")
            
            choice = input().lower()
            if choice in ['y', 'yes', 'نعم']:
                return install_requirements()
            else:
                return False
        
        print("✅ جميع المتطلبات مثبتة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص المتطلبات: {e}")
        return False

def install_requirements():
    """تثبيت المتطلبات"""
    print("\n📥 تثبيت المتطلبات...")
    
    try:
        # تحديث pip أولاً
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # تثبيت المتطلبات
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        
        print("✅ تم تثبيت جميع المتطلبات بنجاح")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المتطلبات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في التثبيت: {e}")
        return False

def setup_database():
    """إعداد قاعدة البيانات"""
    print("\n🗄️ إعداد قاعدة البيانات...")
    
    try:
        # إضافة مسار المشروع
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        
        from database.db_handler import db_handler
        
        # تهيئة قاعدة البيانات
        db_handler.initialize_database()
        print("✅ تم إعداد قاعدة البيانات بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def run_tests():
    """تشغيل الاختبارات"""
    print("\n🧪 تشغيل اختبارات النظام...")
    
    try:
        # تشغيل ملف الاختبار
        result = subprocess.run([sys.executable, "test_system.py"], 
                              capture_output=True, text=True, encoding='utf-8')
        
        print(result.stdout)
        if result.stderr:
            print("تحذيرات:", result.stderr)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبارات: {e}")
        return False

def run_application():
    """تشغيل التطبيق"""
    print("\n🚀 تشغيل التطبيق...")
    
    try:
        # تشغيل التطبيق الرئيسي
        subprocess.run([sys.executable, "main.py"])
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")

def show_menu():
    """عرض القائمة الرئيسية"""
    print("\n" + "="*50)
    print("🎓 نظام إدارة الأكاديمية التعليمية")
    print("="*50)
    print("1. تشغيل التطبيق")
    print("2. تشغيل الاختبارات")
    print("3. تثبيت المتطلبات")
    print("4. إعداد قاعدة البيانات")
    print("5. فحص النظام")
    print("0. خروج")
    print("-"*50)
    
    choice = input("اختر رقم العملية: ").strip()
    return choice

def main():
    """الدالة الرئيسية"""
    print("🎓 مرحباً بك في نظام إدارة الأكاديمية التعليمية")
    
    # فحص إصدار Python
    if not check_python_version():
        input("\nاضغط Enter للخروج...")
        return 1
    
    # إذا تم تمرير معامل، تشغيل مباشر
    if len(sys.argv) > 1:
        if sys.argv[1] == "test":
            return 0 if run_tests() else 1
        elif sys.argv[1] == "setup":
            success = check_requirements() and setup_database()
            return 0 if success else 1
        elif sys.argv[1] == "run":
            if check_requirements():
                run_application()
            return 0
    
    # القائمة التفاعلية
    while True:
        choice = show_menu()
        
        if choice == "1":
            # تشغيل التطبيق
            if check_requirements():
                run_application()
            else:
                print("❌ لا يمكن تشغيل التطبيق بسبب متطلبات مفقودة")
                input("اضغط Enter للمتابعة...")
        
        elif choice == "2":
            # تشغيل الاختبارات
            if check_requirements():
                run_tests()
            else:
                print("❌ لا يمكن تشغيل الاختبارات بسبب متطلبات مفقودة")
            input("اضغط Enter للمتابعة...")
        
        elif choice == "3":
            # تثبيت المتطلبات
            install_requirements()
            input("اضغط Enter للمتابعة...")
        
        elif choice == "4":
            # إعداد قاعدة البيانات
            if check_requirements():
                setup_database()
            else:
                print("❌ يجب تثبيت المتطلبات أولاً")
            input("اضغط Enter للمتابعة...")
        
        elif choice == "5":
            # فحص النظام
            print("\n🔍 فحص شامل للنظام...")
            all_good = True
            
            if not check_requirements():
                all_good = False
            
            if not setup_database():
                all_good = False
            
            if all_good:
                print("\n✅ النظام جاهز للاستخدام!")
            else:
                print("\n❌ يوجد مشاكل في النظام")
            
            input("اضغط Enter للمتابعة...")
        
        elif choice == "0":
            print("👋 شكراً لاستخدام النظام!")
            break
        
        else:
            print("❌ اختيار غير صحيح")
            input("اضغط Enter للمتابعة...")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف البرنامج")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ فادح: {e}")
        sys.exit(1)
