"""
تحكم المعلمين - Teacher Controller
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import date
from decimal import Decimal

from models.teacher import Teacher
from models.user import User
from utils.validators import DataValidator
from utils.logger import activity_logger

class TeacherController:
    """تحكم عمليات المعلمين"""
    
    def __init__(self, current_user: User):
        """
        تهيئة تحكم المعلمين
        
        Args:
            current_user: المستخدم الحالي
        """
        self.current_user = current_user
        self.logger = logging.getLogger(__name__)
    
    def create_teacher(self, teacher_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        إنشاء معلم جديد
        
        Args:
            teacher_data: بيانات المعلم
            
        Returns:
            Dict: نتيجة العملية
        """
        try:
            # التحقق من الصلاحيات (المدير فقط)
            if self.current_user.role != 'admin':
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لإضافة المعلمين'
                }
            
            # التحقق من صحة البيانات
            validation_result = DataValidator.validate_teacher_data(teacher_data)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'message': 'بيانات غير صحيحة',
                    'errors': validation_result['errors']
                }
            
            # تحويل الراتب إلى Decimal إذا كان موجوداً
            if 'salary' in teacher_data and teacher_data['salary']:
                teacher_data['salary'] = Decimal(str(teacher_data['salary']))
            
            # إنشاء المعلم
            teacher = Teacher.create(**teacher_data)
            
            # تسجيل النشاط
            activity_logger.log_user_action(
                self.current_user.id,
                self.current_user.username,
                'إضافة معلم',
                f"تم إضافة المعلم: {teacher.full_name} ({teacher.teacher_code})"
            )
            
            return {
                'success': True,
                'message': 'تم إنشاء المعلم بنجاح',
                'teacher': teacher.to_dict()
            }
            
        except ValueError as e:
            return {
                'success': False,
                'message': str(e)
            }
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء المعلم: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def get_teacher(self, teacher_id: int) -> Dict[str, Any]:
        """
        الحصول على بيانات معلم
        
        Args:
            teacher_id: معرف المعلم
            
        Returns:
            Dict: بيانات المعلم
        """
        try:
            teacher = Teacher.get_by_id(teacher_id)
            if not teacher:
                return {
                    'success': False,
                    'message': 'المعلم غير موجود'
                }
            
            return {
                'success': True,
                'teacher': teacher.to_dict()
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على المعلم: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def update_teacher(self, teacher_id: int, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        تحديث بيانات معلم
        
        Args:
            teacher_id: معرف المعلم
            update_data: البيانات المحدثة
            
        Returns:
            Dict: نتيجة العملية
        """
        try:
            # التحقق من الصلاحيات
            if self.current_user.role != 'admin':
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لتعديل المعلمين'
                }
            
            teacher = Teacher.get_by_id(teacher_id)
            if not teacher:
                return {
                    'success': False,
                    'message': 'المعلم غير موجود'
                }
            
            # التحقق من صحة البيانات
            validation_result = DataValidator.validate_teacher_data(update_data)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'message': 'بيانات غير صحيحة',
                    'errors': validation_result['errors']
                }
            
            # تحويل الراتب إلى Decimal إذا كان موجوداً
            if 'salary' in update_data and update_data['salary']:
                update_data['salary'] = Decimal(str(update_data['salary']))
            
            # تحديث البيانات
            if teacher.update(**update_data):
                # تسجيل النشاط
                activity_logger.log_user_action(
                    self.current_user.id,
                    self.current_user.username,
                    'تعديل معلم',
                    f"تم تعديل المعلم: {teacher.full_name} ({teacher.teacher_code})"
                )
                
                return {
                    'success': True,
                    'message': 'تم تحديث بيانات المعلم بنجاح',
                    'teacher': teacher.to_dict()
                }
            else:
                return {
                    'success': False,
                    'message': 'فشل في تحديث البيانات'
                }
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديث المعلم: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def delete_teacher(self, teacher_id: int) -> Dict[str, Any]:
        """
        حذف معلم
        
        Args:
            teacher_id: معرف المعلم
            
        Returns:
            Dict: نتيجة العملية
        """
        try:
            # التحقق من الصلاحيات (المدير فقط)
            if self.current_user.role != 'admin':
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لحذف المعلمين'
                }
            
            teacher = Teacher.get_by_id(teacher_id)
            if not teacher:
                return {
                    'success': False,
                    'message': 'المعلم غير موجود'
                }
            
            teacher_name = teacher.full_name
            teacher_code = teacher.teacher_code
            
            if teacher.delete():
                # تسجيل النشاط
                activity_logger.log_user_action(
                    self.current_user.id,
                    self.current_user.username,
                    'حذف معلم',
                    f"تم حذف المعلم: {teacher_name} ({teacher_code})"
                )
                
                return {
                    'success': True,
                    'message': 'تم حذف المعلم بنجاح'
                }
            else:
                return {
                    'success': False,
                    'message': 'فشل في حذف المعلم'
                }
            
        except Exception as e:
            self.logger.error(f"خطأ في حذف المعلم: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def search_teachers(self, search_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        البحث عن المعلمين
        
        Args:
            search_params: معاملات البحث
            
        Returns:
            Dict: نتائج البحث
        """
        try:
            teachers = Teacher.search(
                search_term=search_params.get('search_term'),
                status=search_params.get('status'),
                specialization=search_params.get('specialization'),
                limit=search_params.get('limit', 50)
            )
            
            return {
                'success': True,
                'teachers': [teacher.to_dict() for teacher in teachers],
                'count': len(teachers)
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في البحث عن المعلمين: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def get_teacher_groups(self, teacher_id: int) -> Dict[str, Any]:
        """
        الحصول على مجموعات المعلم
        
        Args:
            teacher_id: معرف المعلم
            
        Returns:
            Dict: مجموعات المعلم
        """
        try:
            teacher = Teacher.get_by_id(teacher_id)
            if not teacher:
                return {
                    'success': False,
                    'message': 'المعلم غير موجود'
                }
            
            groups = teacher.get_groups()
            current_groups = teacher.get_current_groups()
            
            return {
                'success': True,
                'all_groups': groups,
                'current_groups': current_groups
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على مجموعات المعلم: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def get_teacher_workload(self, teacher_id: int) -> Dict[str, Any]:
        """
        الحصول على عبء عمل المعلم
        
        Args:
            teacher_id: معرف المعلم
            
        Returns:
            Dict: عبء العمل
        """
        try:
            teacher = Teacher.get_by_id(teacher_id)
            if not teacher:
                return {
                    'success': False,
                    'message': 'المعلم غير موجود'
                }
            
            workload = teacher.calculate_workload()
            
            return {
                'success': True,
                'workload': workload
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب عبء العمل: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def get_teacher_salary_history(self, teacher_id: int, year: int = None) -> Dict[str, Any]:
        """
        الحصول على تاريخ رواتب المعلم
        
        Args:
            teacher_id: معرف المعلم
            year: السنة (اختياري)
            
        Returns:
            Dict: تاريخ الرواتب
        """
        try:
            # التحقق من الصلاحيات
            if self.current_user.role != 'admin':
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لعرض الرواتب'
                }
            
            teacher = Teacher.get_by_id(teacher_id)
            if not teacher:
                return {
                    'success': False,
                    'message': 'المعلم غير موجود'
                }
            
            salary_history = teacher.get_salary_history(year)
            
            return {
                'success': True,
                'salary_history': salary_history
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على تاريخ الرواتب: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
