"""
نموذج المعلم - Teacher Model
"""

import logging
from datetime import datetime, date
from typing import Optional, Dict, Any, List
from decimal import Decimal
from database.db_handler import db_handler

class Teacher:
    """نموذج المعلم"""
    
    # حالات المعلم المتاحة
    STATUSES = {
        'active': 'نشط',
        'inactive': 'غير نشط',
        'terminated': 'منتهي الخدمة'
    }
    
    def __init__(self, teacher_id: int = None, user_id: int = None,
                 teacher_code: str = None, first_name: str = None,
                 last_name: str = None, phone: str = None, email: str = None,
                 address: str = None, specialization: str = None,
                 qualification: str = None, hire_date: date = None,
                 salary: Decimal = None, status: str = 'active',
                 notes: str = None, photo_path: str = None,
                 created_at: datetime = None, updated_at: datetime = None):
        """
        تهيئة نموذج المعلم
        """
        self.id = teacher_id
        self.user_id = user_id
        self.teacher_code = teacher_code
        self.first_name = first_name
        self.last_name = last_name
        self.phone = phone
        self.email = email
        self.address = address
        self.specialization = specialization
        self.qualification = qualification
        self.hire_date = hire_date or date.today()
        self.salary = salary
        self.status = status
        self.notes = notes
        self.photo_path = photo_path
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()
        self.logger = logging.getLogger(__name__)
    
    @property
    def full_name(self) -> str:
        """الاسم الكامل"""
        return f"{self.first_name} {self.last_name}".strip()
    
    @property
    def years_of_service(self) -> Optional[int]:
        """سنوات الخدمة"""
        if self.hire_date:
            today = date.today()
            return today.year - self.hire_date.year
        return None
    
    @classmethod
    def create(cls, first_name: str, last_name: str, teacher_code: str = None,
               **kwargs) -> 'Teacher':
        """
        إنشاء معلم جديد
        
        Args:
            first_name: الاسم الأول
            last_name: اسم العائلة
            teacher_code: رمز المعلم
            **kwargs: باقي البيانات
            
        Returns:
            Teacher: المعلم الجديد
        """
        # التحقق من صحة البيانات
        if not first_name or not last_name:
            raise ValueError("الاسم الأول واسم العائلة مطلوبان")
        
        # إنشاء رمز المعلم إذا لم يتم تمريره
        if not teacher_code:
            teacher_code = cls._generate_teacher_code()
        
        # التحقق من عدم وجود رمز المعلم مسبقاً
        if cls.get_by_code(teacher_code):
            raise ValueError("رمز المعلم موجود مسبقاً")
        
        # إعداد البيانات
        teacher_data = {
            'teacher_code': teacher_code,
            'first_name': first_name,
            'last_name': last_name,
            'hire_date': date.today(),
            'status': 'active',
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
        
        # إضافة البيانات الإضافية
        allowed_fields = [
            'user_id', 'phone', 'email', 'address', 'specialization',
            'qualification', 'salary', 'notes', 'photo_path'
        ]
        
        for field in allowed_fields:
            if field in kwargs and kwargs[field] is not None:
                teacher_data[field] = kwargs[field]
        
        # إدراج في قاعدة البيانات
        teacher_id = db_handler.insert('teachers', teacher_data)
        
        # إنشاء كائن المعلم
        teacher = cls(
            teacher_id=teacher_id,
            teacher_code=teacher_code,
            first_name=first_name,
            last_name=last_name,
            **kwargs
        )
        
        teacher.logger.info(f"تم إنشاء معلم جديد: {teacher.full_name} ({teacher_code})")
        return teacher
    
    @classmethod
    def get_by_id(cls, teacher_id: int) -> Optional['Teacher']:
        """الحصول على معلم بالمعرف"""
        query = "SELECT * FROM teachers WHERE id = ?"
        row = db_handler.fetch_one(query, (teacher_id,))
        
        if row:
            return cls._from_row(row)
        return None
    
    @classmethod
    def get_by_code(cls, teacher_code: str) -> Optional['Teacher']:
        """الحصول على معلم بالرمز"""
        query = "SELECT * FROM teachers WHERE teacher_code = ?"
        row = db_handler.fetch_one(query, (teacher_code,))
        
        if row:
            return cls._from_row(row)
        return None
    
    @classmethod
    def get_by_user_id(cls, user_id: int) -> Optional['Teacher']:
        """الحصول على معلم بمعرف المستخدم"""
        query = "SELECT * FROM teachers WHERE user_id = ?"
        row = db_handler.fetch_one(query, (user_id,))
        
        if row:
            return cls._from_row(row)
        return None
    
    @classmethod
    def search(cls, search_term: str = None, status: str = None,
               specialization: str = None, limit: int = None) -> List['Teacher']:
        """
        البحث عن المعلمين
        
        Args:
            search_term: مصطلح البحث (اسم أو رمز)
            status: الحالة
            specialization: التخصص
            limit: عدد النتائج المحدود
            
        Returns:
            List[Teacher]: قائمة المعلمين
        """
        query = "SELECT * FROM teachers WHERE 1=1"
        params = []
        
        if search_term:
            query += " AND (first_name LIKE ? OR last_name LIKE ? OR teacher_code LIKE ?)"
            search_pattern = f"%{search_term}%"
            params.extend([search_pattern, search_pattern, search_pattern])
        
        if status:
            query += " AND status = ?"
            params.append(status)
        
        if specialization:
            query += " AND specialization LIKE ?"
            params.append(f"%{specialization}%")
        
        query += " ORDER BY first_name, last_name"
        
        if limit:
            query += f" LIMIT {limit}"
        
        rows = db_handler.fetch_all(query, tuple(params) if params else None)
        return [cls._from_row(row) for row in rows]
    
    @classmethod
    def get_all(cls, status: str = None) -> List['Teacher']:
        """الحصول على جميع المعلمين"""
        return cls.search(status=status)
    
    @classmethod
    def _generate_teacher_code(cls) -> str:
        """إنشاء رمز معلم جديد"""
        # الحصول على آخر رمز معلم
        query = "SELECT teacher_code FROM teachers ORDER BY id DESC LIMIT 1"
        row = db_handler.fetch_one(query)
        
        if row and row['teacher_code']:
            # استخراج الرقم من آخر رمز
            last_code = row['teacher_code']
            if last_code.startswith('TCH'):
                try:
                    last_number = int(last_code[3:])
                    new_number = last_number + 1
                except ValueError:
                    new_number = 1
            else:
                new_number = 1
        else:
            new_number = 1
        
        return f"TCH{new_number:04d}"  # TCH0001, TCH0002, etc.
    
    def update(self, **kwargs) -> bool:
        """تحديث بيانات المعلم"""
        try:
            # الحقول المسموح بتحديثها
            allowed_fields = [
                'first_name', 'last_name', 'phone', 'email', 'address',
                'specialization', 'qualification', 'salary', 'status',
                'notes', 'photo_path'
            ]
            
            data = {}
            for key, value in kwargs.items():
                if key in allowed_fields:
                    data[key] = value
                    setattr(self, key, value)
            
            if data:
                data['updated_at'] = datetime.now()
                rows_affected = db_handler.update('teachers', data, 'id = ?', (self.id,))
                
                if rows_affected > 0:
                    self.updated_at = datetime.now()
                    self.logger.info(f"تم تحديث بيانات المعلم: {self.full_name}")
                    return True
            
            return False
        except Exception as e:
            self.logger.error(f"خطأ في تحديث بيانات المعلم: {e}")
            return False
    
    def delete(self) -> bool:
        """حذف المعلم"""
        try:
            rows_affected = db_handler.delete('teachers', 'id = ?', (self.id,))
            
            if rows_affected > 0:
                self.logger.info(f"تم حذف المعلم: {self.full_name}")
                return True
            
            return False
        except Exception as e:
            self.logger.error(f"خطأ في حذف المعلم: {e}")
            return False
    
    def get_groups(self) -> List[Dict]:
        """الحصول على مجموعات المعلم"""
        query = """
        SELECT g.*, c.name as course_name, c.duration_hours,
               COUNT(sg.student_id) as enrolled_students
        FROM groups g
        JOIN courses c ON g.course_id = c.id
        LEFT JOIN student_groups sg ON g.id = sg.group_id AND sg.status = 'active'
        WHERE g.teacher_id = ?
        GROUP BY g.id
        ORDER BY g.start_date DESC
        """
        
        rows = db_handler.fetch_all(query, (self.id,))
        return [dict(row) for row in rows]
    
    def get_current_groups(self) -> List[Dict]:
        """الحصول على المجموعات الحالية للمعلم"""
        query = """
        SELECT g.*, c.name as course_name, c.duration_hours,
               COUNT(sg.student_id) as enrolled_students
        FROM groups g
        JOIN courses c ON g.course_id = c.id
        LEFT JOIN student_groups sg ON g.id = sg.group_id AND sg.status = 'active'
        WHERE g.teacher_id = ? AND g.status = 'active'
        GROUP BY g.id
        ORDER BY g.start_date DESC
        """
        
        rows = db_handler.fetch_all(query, (self.id,))
        return [dict(row) for row in rows]
    
    def get_salary_history(self, year: int = None) -> List[Dict]:
        """الحصول على تاريخ الرواتب"""
        query = "SELECT * FROM teacher_salaries WHERE teacher_id = ?"
        params = [self.id]
        
        if year:
            query += " AND year = ?"
            params.append(year)
        
        query += " ORDER BY year DESC, month DESC"
        
        rows = db_handler.fetch_all(query, tuple(params))
        return [dict(row) for row in rows]
    
    def get_monthly_salary(self, month: int, year: int) -> Optional[Dict]:
        """الحصول على راتب شهر معين"""
        query = "SELECT * FROM teacher_salaries WHERE teacher_id = ? AND month = ? AND year = ?"
        row = db_handler.fetch_one(query, (self.id, month, year))
        
        if row:
            return dict(row)
        return None
    
    def calculate_workload(self) -> Dict[str, Any]:
        """حساب عبء العمل للمعلم"""
        # عدد المجموعات النشطة
        active_groups = len(self.get_current_groups())
        
        # عدد الطلاب الإجمالي
        query = """
        SELECT COUNT(DISTINCT sg.student_id) as total_students
        FROM groups g
        JOIN student_groups sg ON g.id = sg.group_id
        WHERE g.teacher_id = ? AND g.status = 'active' AND sg.status = 'active'
        """
        
        row = db_handler.fetch_one(query, (self.id,))
        total_students = row['total_students'] if row else 0
        
        # عدد الساعات الأسبوعية
        query = """
        SELECT SUM(
            CASE 
                WHEN g.end_time IS NOT NULL AND g.start_time IS NOT NULL 
                THEN (julianday(g.end_time) - julianday(g.start_time)) * 24
                ELSE 0 
            END
        ) as weekly_hours
        FROM groups g
        WHERE g.teacher_id = ? AND g.status = 'active'
        """
        
        row = db_handler.fetch_one(query, (self.id,))
        weekly_hours = row['weekly_hours'] if row and row['weekly_hours'] else 0
        
        return {
            'active_groups': active_groups,
            'total_students': total_students,
            'weekly_hours': round(weekly_hours, 2),
            'avg_students_per_group': round(total_students / active_groups, 1) if active_groups > 0 else 0
        }
    
    @classmethod
    def _from_row(cls, row) -> 'Teacher':
        """إنشاء كائن معلم من سجل قاعدة البيانات"""
        return cls(
            teacher_id=row['id'],
            user_id=row['user_id'],
            teacher_code=row['teacher_code'],
            first_name=row['first_name'],
            last_name=row['last_name'],
            phone=row['phone'],
            email=row['email'],
            address=row['address'],
            specialization=row['specialization'],
            qualification=row['qualification'],
            hire_date=date.fromisoformat(row['hire_date']) if row['hire_date'] else None,
            salary=Decimal(str(row['salary'])) if row['salary'] else None,
            status=row['status'],
            notes=row['notes'],
            photo_path=row['photo_path'],
            created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
            updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل المعلم إلى قاموس"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'teacher_code': self.teacher_code,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'full_name': self.full_name,
            'phone': self.phone,
            'email': self.email,
            'address': self.address,
            'specialization': self.specialization,
            'qualification': self.qualification,
            'hire_date': self.hire_date.isoformat() if self.hire_date else None,
            'years_of_service': self.years_of_service,
            'salary': float(self.salary) if self.salary else None,
            'status': self.status,
            'status_name': self.STATUSES.get(self.status, self.status),
            'notes': self.notes,
            'photo_path': self.photo_path,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __str__(self):
        return f"Teacher(id={self.id}, code='{self.teacher_code}', name='{self.full_name}')"
    
    def __repr__(self):
        return self.__str__()
