"""
تحكم الطلاب - Student Controller
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import date

from models.student import Student
from models.user import User
from utils.validators import DataValidator
from utils.logger import activity_logger

class StudentController:
    """تحكم عمليات الطلاب"""
    
    def __init__(self, current_user: User):
        """
        تهيئة تحكم الطلاب
        
        Args:
            current_user: المستخدم الحالي
        """
        self.current_user = current_user
        self.logger = logging.getLogger(__name__)
    
    def create_student(self, student_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        إنشاء طالب جديد
        
        Args:
            student_data: بيانات الطالب
            
        Returns:
            Dict: نتيجة العملية
        """
        try:
            # التحقق من الصلاحيات
            if not self.current_user.has_permission('manage_students'):
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لإضافة الطلاب'
                }
            
            # التحقق من صحة البيانات
            validation_result = DataValidator.validate_student_data(student_data)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'message': 'بيانات غير صحيحة',
                    'errors': validation_result['errors']
                }
            
            # إنشاء الطالب
            student = Student.create(**student_data)
            
            # تسجيل النشاط
            activity_logger.log_user_action(
                self.current_user.id,
                self.current_user.username,
                'إضافة طالب',
                f"تم إضافة الطالب: {student.full_name} ({student.student_code})"
            )
            
            return {
                'success': True,
                'message': 'تم إنشاء الطالب بنجاح',
                'student': student.to_dict()
            }
            
        except ValueError as e:
            return {
                'success': False,
                'message': str(e)
            }
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الطالب: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def get_student(self, student_id: int) -> Dict[str, Any]:
        """
        الحصول على بيانات طالب
        
        Args:
            student_id: معرف الطالب
            
        Returns:
            Dict: بيانات الطالب
        """
        try:
            # التحقق من الصلاحيات
            if not self.current_user.has_permission('view_students'):
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لعرض الطلاب'
                }
            
            student = Student.get_by_id(student_id)
            if not student:
                return {
                    'success': False,
                    'message': 'الطالب غير موجود'
                }
            
            return {
                'success': True,
                'student': student.to_dict()
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الطالب: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def update_student(self, student_id: int, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        تحديث بيانات طالب
        
        Args:
            student_id: معرف الطالب
            update_data: البيانات المحدثة
            
        Returns:
            Dict: نتيجة العملية
        """
        try:
            # التحقق من الصلاحيات
            if not self.current_user.has_permission('manage_students'):
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لتعديل الطلاب'
                }
            
            student = Student.get_by_id(student_id)
            if not student:
                return {
                    'success': False,
                    'message': 'الطالب غير موجود'
                }
            
            # التحقق من صحة البيانات
            validation_result = DataValidator.validate_student_data(update_data)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'message': 'بيانات غير صحيحة',
                    'errors': validation_result['errors']
                }
            
            # تحديث البيانات
            if student.update(**update_data):
                # تسجيل النشاط
                activity_logger.log_user_action(
                    self.current_user.id,
                    self.current_user.username,
                    'تعديل طالب',
                    f"تم تعديل الطالب: {student.full_name} ({student.student_code})"
                )
                
                return {
                    'success': True,
                    'message': 'تم تحديث بيانات الطالب بنجاح',
                    'student': student.to_dict()
                }
            else:
                return {
                    'success': False,
                    'message': 'فشل في تحديث البيانات'
                }
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديث الطالب: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def delete_student(self, student_id: int) -> Dict[str, Any]:
        """
        حذف طالب
        
        Args:
            student_id: معرف الطالب
            
        Returns:
            Dict: نتيجة العملية
        """
        try:
            # التحقق من الصلاحيات (المدير فقط)
            if self.current_user.role != 'admin':
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لحذف الطلاب'
                }
            
            student = Student.get_by_id(student_id)
            if not student:
                return {
                    'success': False,
                    'message': 'الطالب غير موجود'
                }
            
            student_name = student.full_name
            student_code = student.student_code
            
            if student.delete():
                # تسجيل النشاط
                activity_logger.log_user_action(
                    self.current_user.id,
                    self.current_user.username,
                    'حذف طالب',
                    f"تم حذف الطالب: {student_name} ({student_code})"
                )
                
                return {
                    'success': True,
                    'message': 'تم حذف الطالب بنجاح'
                }
            else:
                return {
                    'success': False,
                    'message': 'فشل في حذف الطالب'
                }
            
        except Exception as e:
            self.logger.error(f"خطأ في حذف الطالب: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def search_students(self, search_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        البحث عن الطلاب
        
        Args:
            search_params: معاملات البحث
            
        Returns:
            Dict: نتائج البحث
        """
        try:
            # التحقق من الصلاحيات
            if not self.current_user.has_permission('view_students'):
                return {
                    'success': False,
                    'message': 'ليس لديك صلاحية لعرض الطلاب'
                }
            
            students = Student.search(
                search_term=search_params.get('search_term'),
                status=search_params.get('status'),
                gender=search_params.get('gender'),
                limit=search_params.get('limit', 50)
            )
            
            return {
                'success': True,
                'students': [student.to_dict() for student in students],
                'count': len(students)
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في البحث عن الطلاب: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def get_student_groups(self, student_id: int) -> Dict[str, Any]:
        """
        الحصول على مجموعات الطالب
        
        Args:
            student_id: معرف الطالب
            
        Returns:
            Dict: مجموعات الطالب
        """
        try:
            student = Student.get_by_id(student_id)
            if not student:
                return {
                    'success': False,
                    'message': 'الطالب غير موجود'
                }
            
            groups = student.get_groups()
            
            return {
                'success': True,
                'groups': groups
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على مجموعات الطالب: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def get_student_payments(self, student_id: int) -> Dict[str, Any]:
        """
        الحصول على مدفوعات الطالب
        
        Args:
            student_id: معرف الطالب
            
        Returns:
            Dict: مدفوعات الطالب
        """
        try:
            student = Student.get_by_id(student_id)
            if not student:
                return {
                    'success': False,
                    'message': 'الطالب غير موجود'
                }
            
            payments = student.get_payments()
            
            return {
                'success': True,
                'payments': payments
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على مدفوعات الطالب: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
    
    def get_student_attendance_summary(self, student_id: int) -> Dict[str, Any]:
        """
        الحصول على ملخص حضور الطالب
        
        Args:
            student_id: معرف الطالب
            
        Returns:
            Dict: ملخص الحضور
        """
        try:
            student = Student.get_by_id(student_id)
            if not student:
                return {
                    'success': False,
                    'message': 'الطالب غير موجود'
                }
            
            attendance_summary = student.get_attendance_summary()
            
            return {
                'success': True,
                'attendance_summary': attendance_summary
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على ملخص الحضور: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
