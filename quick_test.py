#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للنظام
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic():
    """اختبار أساسي"""
    print("🔍 اختبار أساسي...")
    
    try:
        # اختبار قاعدة البيانات
        from database.db_handler import db_handler
        db_handler.initialize_database()
        print("✅ قاعدة البيانات تعمل")
        
        # اختبار المستخدم
        from models.user import User
        admin = User.get_by_username('admin')
        if admin:
            print("✅ المستخدم الافتراضي موجود")
            
            # اختبار تسجيل الدخول
            auth = User.authenticate('admin', 'admin123')
            if auth:
                print("✅ تسجيل الدخول يعمل")
            else:
                print("❌ فشل تسجيل الدخول")
        else:
            print("❌ المستخدم الافتراضي غير موجود")
        
        # اختبار إنشاء طالب
        from models.student import Student
        from decimal import Decimal
        
        try:
            student = Student.create(
                first_name="أحمد",
                last_name="محمد",
                email="<EMAIL>"
            )
            print(f"✅ تم إنشاء طالب: {student.student_code}")
        except Exception as e:
            print(f"⚠️ الطالب موجود أو خطأ: {e}")
        
        print("✅ الاختبار الأساسي نجح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    print("🎓 اختبار سريع لنظام إدارة الأكاديمية")
    print("=" * 40)
    
    if test_basic():
        print("\n🎉 النظام يعمل بشكل أساسي!")
        print("يمكنك تشغيل النظام الكامل: python main.py")
    else:
        print("\n❌ يوجد مشاكل في النظام")
    
    print("\nانتهى الاختبار")
